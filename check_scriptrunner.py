#!/usr/bin/env python3
"""
🔍 CHECK SCRIPTRUNNER INSTALLATION
Verify ScriptRunner is installed and accessible
"""

import requests
from requests.auth import HTTPBasicAuth
import json
from datetime import datetime

def check_scriptrunner_installation():
    """Check if <PERSON>riptRunner is installed and accessible"""
    print("🔍 CHECKING SCRIPTRUNNER INSTALLATION")
    print("="*60)
    
    base_url = 'https://art.iyc.ishafoundation.org'
    auth = HTTPBasicAuth('script', 'createpage')
    
    # Test ScriptRunner endpoints
    scriptrunner_endpoints = [
        "/rest/scriptrunner/latest/user/exec",
        "/rest/scriptrunner/latest/custom",
        "/plugins/servlet/scriptrunner/admin",
    ]
    
    scriptrunner_available = False
    
    for endpoint in scriptrunner_endpoints:
        try:
            url = f"{base_url}{endpoint}"
            print(f"🔗 Testing: {url}")
            
            response = requests.get(url, auth=auth, timeout=10)
            print(f"   📤 Response: {response.status_code}")
            
            if response.status_code in [200, 401, 403]:  # These indicate ScriptRunner exists
                print(f"   ✅ ScriptRunner endpoint accessible")
                scriptrunner_available = True
            elif response.status_code == 404:
                print(f"   ❌ ScriptRunner endpoint not found")
            else:
                print(f"   ⚠️ Unexpected status: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ Error: {str(e)}")
    
    return scriptrunner_available

def check_admin_access():
    """Check if we have admin access to Confluence"""
    print(f"\n🔐 CHECKING ADMIN ACCESS")
    print("-" * 40)
    
    base_url = 'https://art.iyc.ishafoundation.org'
    auth = HTTPBasicAuth('script', 'createpage')
    
    try:
        # Test admin endpoints
        admin_url = f"{base_url}/rest/api/space/TEMP"
        response = requests.get(admin_url, auth=auth, timeout=10)
        
        print(f"📤 Admin API response: {response.status_code}")
        
        if response.status_code == 200:
            space_data = response.json()
            print(f"✅ Admin access confirmed")
            print(f"📚 Space: {space_data.get('name', 'Unknown')}")
            return True
        else:
            print(f"❌ Limited admin access: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error checking admin access: {str(e)}")
        return False

def main():
    """Check ScriptRunner installation and access"""
    print("🔍 SCRIPTRUNNER INSTALLATION CHECK")
    print("🕐 Started at:", datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
    
    # Check ScriptRunner
    scriptrunner_ok = check_scriptrunner_installation()
    
    # Check admin access
    admin_ok = check_admin_access()
    
    print("\n" + "="*60)
    print("🎯 INSTALLATION CHECK COMPLETE")
    print("="*60)
    
    print(f"\n📊 RESULTS:")
    print(f"   🔧 ScriptRunner: {'AVAILABLE' if scriptrunner_ok else 'NOT FOUND'}")
    print(f"   🔐 Admin Access: {'CONFIRMED' if admin_ok else 'LIMITED'}")
    
    if scriptrunner_ok and admin_ok:
        print(f"\n🎉 READY TO PROCEED!")
        print(f"   ✅ ScriptRunner is installed and accessible")
        print(f"   ✅ Admin access confirmed")
        print(f"   🚀 Next: Run the setup script")
    else:
        print(f"\n⚠️ SETUP REQUIRED:")
        if not scriptrunner_ok:
            print(f"   📦 Install ScriptRunner for Confluence")
        if not admin_ok:
            print(f"   🔐 Verify admin credentials")

if __name__ == "__main__":
    main()
