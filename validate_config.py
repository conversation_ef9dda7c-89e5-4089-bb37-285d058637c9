#!/usr/bin/env python3
"""
🔧 Configuration Validator for JIRA-Confluence Sync
Validates all configuration settings and connectivity
"""

import requests
from requests.auth import HTTPBasicAuth
import base64
import json
from datetime import datetime

def validate_jira_connection():
    """Validate JIRA connection and credentials"""
    print("\n🔍 Validating JIRA Connection...")
    
    jira_url = "https://servicedesk.isha.in"
    decoded_str = base64.b64decode('YXJjaGl2ZXMuc2NyaXB0OkB1dG9tYXRl').decode('utf-8')
    username, password = decoded_str.split(":")
    auth = HTTPBasicAuth(username, password)
    
    try:
        # Test basic connectivity
        response = requests.get(f"{jira_url}/rest/api/2/myself", auth=auth, timeout=10)
        if response.status_code == 200:
            user_info = response.json()
            print(f"✅ JIRA Connection: SUCCESS")
            print(f"   👤 Connected as: {user_info.get('displayName', 'Unknown')}")
            print(f"   📧 Email: {user_info.get('emailAddress', 'Unknown')}")
            return True
        else:
            print(f"❌ JIRA Connection: FAILED ({response.status_code})")
            print(f"   Response: {response.text[:200]}")
            return False
    except Exception as e:
        print(f"❌ JIRA Connection: ERROR - {str(e)}")
        return False

def validate_confluence_connection():
    """Validate Confluence connection and credentials"""
    print("\n🔍 Validating Confluence Connection...")
    
    conf_url = 'https://art.iyc.ishafoundation.org'
    username = 'script'
    password = 'createpage'
    
    try:
        # Test basic connectivity
        response = requests.get(f"{conf_url}/rest/api/space", 
                              auth=HTTPBasicAuth(username, password), timeout=10)
        if response.status_code == 200:
            spaces = response.json()
            print(f"✅ Confluence Connection: SUCCESS")
            print(f"   📚 Available spaces: {len(spaces.get('results', []))}")
            return True
        else:
            print(f"❌ Confluence Connection: FAILED ({response.status_code})")
            print(f"   Response: {response.text[:200]}")
            return False
    except Exception as e:
        print(f"❌ Confluence Connection: ERROR - {str(e)}")
        return False

def validate_api_endpoints():
    """Validate custom API endpoints"""
    print("\n🔍 Validating Custom API Endpoints...")
    
    # Test permission API endpoints
    test_endpoints = [
        "https://gnanetra.iyc.ishafoundation.org/ArApi/conf/add-perm",
        "https://gnanetra.iyc.ishafoundation.org/ArApi/conf/rm-perm"
    ]
    
    for endpoint in test_endpoints:
        try:
            # Just test if endpoint is reachable (don't actually call it)
            response = requests.head(endpoint, timeout=5)
            if response.status_code in [200, 400, 405]:  # 405 = Method not allowed is OK
                print(f"✅ Endpoint reachable: {endpoint}")
            else:
                print(f"⚠️ Endpoint status {response.status_code}: {endpoint}")
        except Exception as e:
            print(f"❌ Endpoint unreachable: {endpoint} - {str(e)}")

def test_specific_tickets():
    """Test access to specific JIRA tickets"""
    print("\n🔍 Testing Specific JIRA Tickets...")
    
    jira_url = "https://servicedesk.isha.in"
    api_url = f"{jira_url}/rest/api/2/search"
    decoded_str = base64.b64decode('YXJjaGl2ZXMuc2NyaXB0OkB1dG9tYXRl').decode('utf-8')
    username, password = decoded_str.split(":")
    auth = HTTPBasicAuth(username, password)
    
    test_tickets = ["AT-59896", "AT-59894", "AT-59895", "AT-59898", "AT-59899"]
    
    for ticket in test_tickets:
        try:
            params = {
                'jql': f'key = "{ticket}"',
                'fields': 'key,summary,assignee,customfield_111118'
            }
            
            response = requests.get(api_url, params=params, auth=auth, timeout=10)
            if response.status_code == 200:
                data = response.json()
                if data['issues']:
                    issue = data['issues'][0]
                    assignee = issue['fields']['assignee']
                    confluence_link = issue['fields'].get('customfield_111118')
                    
                    print(f"✅ {ticket}: Found")
                    print(f"   📝 Summary: {issue['fields']['summary'][:50]}...")
                    print(f"   👤 Assignee: {assignee['displayName'] if assignee else 'Unassigned'}")
                    print(f"   🔗 Confluence: {'Yes' if confluence_link else 'No'}")
                else:
                    print(f"❌ {ticket}: Not found")
            else:
                print(f"❌ {ticket}: API error ({response.status_code})")
        except Exception as e:
            print(f"❌ {ticket}: Error - {str(e)}")

def validate_username_mapping():
    """Validate username mapping configuration"""
    print("\n🔍 Validating Username Mapping...")
    
    USERNAME_MAPPING = {
        'yogesh.ramprabhu': 'yogesh ramprabhu',
        'rohit.dutt': 'Rohit Dutt', 
        'Ancy Ravindra': 'Ancy Ravindra',
    }
    
    print(f"📋 Configured mappings: {len(USERNAME_MAPPING)}")
    for jira_user, confluence_user in USERNAME_MAPPING.items():
        print(f"   🔄 {jira_user} → {confluence_user}")
    
    print("✅ Username mapping configuration loaded")

def main():
    """Main validation function"""
    print("=" * 80)
    print("🔧 JIRA-CONFLUENCE SYNC CONFIGURATION VALIDATOR")
    print("=" * 80)
    print(f"🕐 Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    validations = []
    
    # Run all validations
    validations.append(("JIRA Connection", validate_jira_connection()))
    validations.append(("Confluence Connection", validate_confluence_connection()))
    validate_api_endpoints()  # This doesn't return boolean
    validate_username_mapping()  # This doesn't return boolean
    test_specific_tickets()  # This doesn't return boolean
    
    # Summary
    print("\n" + "=" * 80)
    print("📊 VALIDATION SUMMARY")
    print("=" * 80)
    
    passed = sum(1 for _, result in validations if result)
    total = len(validations)
    
    for name, result in validations:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status}: {name}")
    
    print(f"\n📈 Overall: {passed}/{total} core validations passed")
    print(f"🕐 Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    if passed == total:
        print("🎉 ALL CORE VALIDATIONS PASSED!")
        return 0
    else:
        print("⚠️ SOME VALIDATIONS FAILED - Check configuration")
        return 1

if __name__ == "__main__":
    import sys
    exit_code = main()
    sys.exit(exit_code)
