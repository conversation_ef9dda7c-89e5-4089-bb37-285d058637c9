#!/usr/bin/env python3
"""
🔧 CONFLUENCE DIRECT SOLUTION
Direct approach to enable page-level permissions without <PERSON><PERSON>t<PERSON><PERSON><PERSON>
"""

import requests
from requests.auth import HTTPBasicAuth
import json
from datetime import datetime

def enable_page_restrictions_direct():
    """Enable page restrictions using direct Confluence API calls"""
    print("🔧 ENABLING PAGE RESTRICTIONS DIRECTLY")
    print("="*60)
    
    base_url = 'https://art.iyc.ishafoundation.org'
    auth = HTTPBasicAuth('script', 'createpage')
    page_id = "151139217"
    
    # Step 1: Get current page data
    print("1️⃣ Getting current page data...")
    try:
        url = f"{base_url}/rest/api/content/{page_id}?expand=space,restrictions,version"
        response = requests.get(url, auth=auth, timeout=30)
        
        if response.status_code == 200:
            page_data = response.json()
            print(f"   ✅ Page found: {page_data.get('title', 'Unknown')}")
            print(f"   📚 Space: {page_data.get('space', {}).get('name', 'Unknown')}")
            
            current_version = page_data.get('version', {}).get('number', 1)
            print(f"   📝 Current version: {current_version}")
            
            # Check current restrictions
            restrictions = page_data.get('restrictions', {})
            print(f"   🔒 Current restrictions: {json.dumps(restrictions, indent=2)}")
            
            return page_data
        else:
            print(f"   ❌ Could not get page data: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"   ❌ Error getting page data: {str(e)}")
        return None

def create_page_restrictions(page_data, username):
    """Create page restrictions using Confluence API"""
    print(f"\n2️⃣ Creating page restrictions for {username}...")
    
    base_url = 'https://art.iyc.ishafoundation.org'
    auth = HTTPBasicAuth('script', 'createpage')
    page_id = page_data.get('id')
    
    try:
        # Method 1: Try to create restrictions using content restriction API
        restriction_url = f"{base_url}/rest/api/content/{page_id}/restriction"
        
        # Create restriction data
        restriction_data = {
            "operation": "read",
            "restrictions": {
                "user": [
                    {
                        "type": "known",
                        "username": username
                    }
                ]
            }
        }
        
        headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        }
        
        # Try POST (create new restriction)
        print(f"   📤 Attempting POST restriction...")
        post_response = requests.post(restriction_url, 
                                    json=restriction_data, 
                                    auth=auth, 
                                    headers=headers,
                                    timeout=30)
        
        print(f"   📤 POST response: {post_response.status_code}")
        if post_response.text:
            print(f"      Response: {post_response.text[:300]}")
        
        if post_response.status_code in [200, 201]:
            print(f"   ✅ Restriction created successfully!")
            return True
        
        # Try PUT (update existing restriction)
        print(f"   📤 Attempting PUT restriction...")
        put_response = requests.put(restriction_url, 
                                  json=restriction_data, 
                                  auth=auth, 
                                  headers=headers,
                                  timeout=30)
        
        print(f"   📤 PUT response: {put_response.status_code}")
        if put_response.text:
            print(f"      Response: {put_response.text[:300]}")
        
        if put_response.status_code in [200, 201]:
            print(f"   ✅ Restriction updated successfully!")
            return True
        
        # Method 2: Try updating page content with restrictions
        print(f"   📤 Attempting page content update with restrictions...")
        return update_page_with_restrictions(page_data, username)
        
    except Exception as e:
        print(f"   ❌ Error creating restrictions: {str(e)}")
        return False

def update_page_with_restrictions(page_data, username):
    """Update page content to include restrictions"""
    base_url = 'https://art.iyc.ishafoundation.org'
    auth = HTTPBasicAuth('script', 'createpage')
    page_id = page_data.get('id')
    
    try:
        # Prepare updated page data with restrictions
        current_version = page_data.get('version', {}).get('number', 1)
        
        update_data = {
            "id": page_id,
            "type": "page",
            "title": page_data.get('title'),
            "space": {
                "key": page_data.get('space', {}).get('key')
            },
            "version": {
                "number": current_version + 1
            },
            "restrictions": {
                "read": {
                    "restrictions": {
                        "user": [
                            {
                                "type": "known",
                                "username": username
                            }
                        ]
                    }
                }
            }
        }
        
        # Add body if it exists
        if 'body' in page_data:
            update_data['body'] = page_data['body']
        else:
            # Get current body
            body_url = f"{base_url}/rest/api/content/{page_id}?expand=body.storage"
            body_response = requests.get(body_url, auth=auth, timeout=30)
            if body_response.status_code == 200:
                body_data = body_response.json()
                update_data['body'] = body_data.get('body', {})
        
        # Update the page
        update_url = f"{base_url}/rest/api/content/{page_id}"
        headers = {'Content-Type': 'application/json'}
        
        response = requests.put(update_url, 
                              json=update_data, 
                              auth=auth, 
                              headers=headers,
                              timeout=30)
        
        print(f"   📤 Page update response: {response.status_code}")
        if response.text:
            print(f"      Response: {response.text[:300]}")
        
        if response.status_code == 200:
            print(f"   ✅ Page updated with restrictions!")
            return True
        else:
            print(f"   ❌ Page update failed")
            return False
            
    except Exception as e:
        print(f"   ❌ Error updating page: {str(e)}")
        return False

def test_space_configuration():
    """Test and configure space-level settings"""
    print(f"\n3️⃣ Testing space configuration...")
    
    base_url = 'https://art.iyc.ishafoundation.org'
    auth = HTTPBasicAuth('script', 'createpage')
    
    try:
        # Get space information
        space_url = f"{base_url}/rest/api/space/TEMP?expand=permissions"
        response = requests.get(space_url, auth=auth, timeout=30)
        
        if response.status_code == 200:
            space_data = response.json()
            print(f"   ✅ Space accessible: {space_data.get('name')}")
            
            permissions = space_data.get('permissions', [])
            print(f"   🔒 Space permissions: {len(permissions)}")
            
            # Check if space allows page restrictions
            for perm in permissions[:3]:
                print(f"      📝 {perm.get('operation', 'Unknown')}: {perm.get('anonymousAccess', False)}")
            
            return True
        else:
            print(f"   ❌ Could not access space: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Error checking space: {str(e)}")
        return False

def verify_restrictions_working(page_id, username):
    """Verify that restrictions are working"""
    print(f"\n4️⃣ Verifying restrictions for {username}...")
    
    base_url = 'https://art.iyc.ishafoundation.org'
    auth = HTTPBasicAuth('script', 'createpage')
    
    try:
        url = f"{base_url}/rest/api/content/{page_id}?expand=restrictions.read.restrictions.user"
        response = requests.get(url, auth=auth, timeout=30)
        
        if response.status_code == 200:
            page_data = response.json()
            restrictions = page_data.get('restrictions', {}).get('read', {}).get('restrictions', {})
            users_with_access = restrictions.get('user', {}).get('results', [])
            
            usernames = [user.get('username') for user in users_with_access if user.get('username')]
            print(f"   📋 Users with access: {usernames}")
            
            if username in usernames:
                print(f"   ✅ SUCCESS: {username} has page access!")
                return True
            else:
                print(f"   ❌ FAILED: {username} does not have page access")
                return False
        else:
            print(f"   ❌ Could not verify restrictions: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Error verifying restrictions: {str(e)}")
        return False

def main():
    """Run the complete direct solution"""
    print("🔧 CONFLUENCE DIRECT SOLUTION")
    print("🕐 Started at:", datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
    
    test_username = "rohit.dutt"
    
    # Step 1: Get page data
    page_data = enable_page_restrictions_direct()
    if not page_data:
        print("❌ Could not get page data - aborting")
        return
    
    # Step 2: Test space configuration
    space_ok = test_space_configuration()
    
    # Step 3: Create page restrictions
    if page_data:
        restriction_created = create_page_restrictions(page_data, test_username)
        
        # Step 4: Verify restrictions
        if restriction_created:
            import time
            time.sleep(2)  # Wait for changes to propagate
            verification_result = verify_restrictions_working(page_data.get('id'), test_username)
        else:
            verification_result = False
    else:
        restriction_created = False
        verification_result = False
    
    # Summary
    print("\n" + "="*60)
    print("🎯 DIRECT SOLUTION COMPLETE")
    print("="*60)
    
    print(f"\n📊 RESULTS:")
    print(f"   📄 Page data: {'RETRIEVED' if page_data else 'FAILED'}")
    print(f"   🏢 Space config: {'OK' if space_ok else 'NEEDS WORK'}")
    print(f"   🔒 Restrictions: {'CREATED' if restriction_created else 'FAILED'}")
    print(f"   ✅ Verification: {'SUCCESS' if verification_result else 'FAILED'}")
    
    if verification_result:
        print(f"\n🎉 SUCCESS: Direct page restrictions are working!")
        print(f"   ✅ User {test_username} now has page access")
        print(f"   ✅ User appears in restrictions tab")
        print(f"   🚀 Ready to update Python script")
    else:
        print(f"\n⚠️ PARTIAL SUCCESS: Some issues remain")
        print(f"   💡 The page restrictions API may have limitations")
        print(f"   🔄 Consider using workflow assignment as fallback")

if __name__ == "__main__":
    main()
