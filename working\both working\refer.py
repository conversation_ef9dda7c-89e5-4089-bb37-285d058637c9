import requests
from requests.auth import HTTPBasicAuth
import json
import time
import datetime
import base64
import urllib.parse

# ============================================================================
# 1. INITIALIZATION & SETUP
# ============================================================================

# Global variables
totalupdates = 0
success = 1

# Time range for processing (last 1 minute)
time_now = datetime.datetime.now().strftime("%Y-%m-%d %H:%M")
time_one_minute_ago = (datetime.datetime.now() - datetime.timedelta(minutes=1)).strftime("%Y-%m-%d %H:%M")

# Confluence configuration
username = 'script'
password = 'createpage'
confUrl = 'https://art.iyc.ishafoundation.org'
confcwUrl = 'https://art.iyc.ishafoundation.org/rest/cw/1/content'

# JIRA configuration
jira_url = "https://servicedesk.isha.in"
decoded_str = base64.b64decode('YXJjaGl2ZXMuc2NyaXB0OkB1dG9tYXRl').decode('utf-8')
Cusername, Cpassword = decoded_str.split(":")
auth = HTTPBasicAuth(Cusername, Cpassword)
api_url = f"{jira_url}/rest/api/2/search"

# Status references (for mapping only - we don't update status)
Statuses = ['Transcription', 'Transcribed', 'I Proofed', 'II Proofed', 'Proof-Read']
TrStatuses = ['Transcription', 'I Proofing', 'II Proofing', 'Proof-Reading']

# Session for connection pooling
session = requests.Session()

print("🚀 JIRA-Confluence Assignee Sync Initialized")
print(f"⏰ Processing updates from {time_one_minute_ago} to {time_now}")

# ============================================================================
# 2. FETCH JIRA TICKETS (LAST 1 MINUTE)
# ============================================================================

def fetch_jira_tickets():
    """Fetch JIRA tickets updated in the last minute with retry mechanism"""
    
    # Build JQL query for recently updated tickets
    query = f'''project = "Ar Transcription" 
                AND type = "Not Transcribed Contents" 
                AND updated >= "{time_one_minute_ago}" 
                AND updated < "{time_now}"'''
    
    print(f"📋 JQL Query: {query}")
    
    all_issues = []
    max_results = 500
    start_at = 0
    
    while True:
        params = {
            'jql': query,
            'startAt': start_at,
            'maxResults': max_results,
            'fields': 'key,summary,assignee,customfield_111118'  # Only needed fields
        }
        
        # Retry mechanism (up to 20 attempts)
        max_tries = 20
        tries = 0
        
        while tries < max_tries:
            try:
                response = session.get(api_url, params=params, auth=auth)
                time.sleep(0.2)  # Rate limiting
                
                if response.status_code == 200:
                    break
                else:
                    tries += 1
                    print(f'⚠️ JIRA API retry {tries}/{max_tries} - Status: {response.status_code}')
                    time.sleep(5)
                    
            except Exception as e:
                tries += 1
                print(f'⚠️ JIRA API error (try {tries}/{max_tries}): {str(e)}')
                time.sleep(5)
        
        if tries >= max_tries:
            print("❌ Failed to fetch JIRA issues after maximum retries")
            break
            
        data = response.json()
        all_issues.extend(data['issues'])
        
        # Check if more pages exist
        if start_at + max_results >= data['total']:
            break
        start_at += max_results
    
    print(f"📊 Fetched {len(all_issues)} JIRA issues")
    return all_issues

# ============================================================================
# 3. FIND MATCHING CONFLUENCE PAGE
# ============================================================================

def get_confluence_page_id(jira_key, confluence_link):
    """Extract Confluence page ID from JIRA custom field"""
    
    if not confluence_link or confluence_link.strip() == '':
        print(f'⚠️ {jira_key}: No Confluence link found')
        return None
    
    # Extract page title from link
    if isinstance(confluence_link, str):
        title = confluence_link.split('/')[-1].strip()
    else:
        print(f'⚠️ {jira_key}: Invalid Confluence link format')
        return None
    
    if not title:
        print(f'⚠️ {jira_key}: Could not extract page title from link')
        return None
    
    # Search for Confluence page by title
    encoded_title = urllib.parse.quote(title)
    url = f'{confUrl}/rest/api/content/?title={encoded_title}'
    
    try:
        response = requests.get(url, auth=HTTPBasicAuth(username, password), timeout=30)
        
        if response.status_code != 200:
            print(f'❌ {jira_key}: Confluence API error {response.status_code}')
            return None
        
        page_data = response.json()
        
        if 'results' not in page_data or len(page_data['results']) == 0:
            print(f'❌ {jira_key}: Confluence page "{title}" not found')
            return None
        
        page_id = page_data['results'][0]['id']
        print(f'✅ {jira_key}: Found Confluence page ID {page_id} for "{title}"')
        return page_id
        
    except Exception as e:
        print(f'❌ {jira_key}: Error finding Confluence page - {str(e)}')
        return None

# ============================================================================
# 4. READ CONFLUENCE PAGE CURRENT STATE
# ============================================================================

def get_confluence_assignee(page_id):
    """Get current assignee from Confluence page"""
    
    url = f'{confcwUrl}/{page_id}/status?expand=approvals'
    
    try:
        response = requests.get(url, auth=HTTPBasicAuth(username, password))
        
        if response.status_code != 200:
            print(f'❌ Page {page_id}: Could not get Confluence status - {response.status_code}')
            return None
        
        page_data = response.json()
        
        # Extract current assignee from approvals
        current_assignee = ''
        if 'approvals' in page_data and len(page_data['approvals']) > 0:
            approvers = page_data['approvals'][0].get('approvers', [])
            if approvers and len(approvers) > 0:
                if 'user' in approvers[0] and approvers[0]['user']:
                    current_assignee = approvers[0]['user']['name']
        
        return current_assignee
        
    except Exception as e:
        print(f'❌ Page {page_id}: Error getting assignee - {str(e)}')
        return None

# ============================================================================
# 5. COMPARE JIRA VS CONFLUENCE
# ============================================================================

def assignees_match(jira_assignee, confluence_assignee):
    """Compare JIRA and Confluence assignees"""
    
    # Normalize empty values
    jira_assignee = jira_assignee or ''
    confluence_assignee = confluence_assignee or ''
    
    return jira_assignee == confluence_assignee

# ============================================================================
# 6. UPDATE CONFLUENCE ASSIGNEE (IF MISMATCH)
# ============================================================================

def update_confluence_assignee(page_id, jira_assignee):
    """Update Confluence page assignee to match JIRA"""
    
    # Get current workflow state to determine assignment stage
    url = f'{confcwUrl}/{page_id}/status?expand=approvals'
    
    try:
        response = requests.get(url, auth=HTTPBasicAuth(username, password))
        if response.status_code != 200:
            print(f'❌ Page {page_id}: Could not get workflow state')
            return False
        
        page_data = response.json()
        current_state = page_data['state']['name']
        
        # Map Confluence states to assignment stages
        stage_mapping = {
            'Transcription': 'Transcription',
            'Transcribed': 'I Proofing', 
            'I Proofed': 'II Proofing',
            'II Proofed': 'Proof-Reading'
        }
        
        if current_state not in stage_mapping:
            print(f'⚠️ Page {page_id}: Unknown workflow state "{current_state}"')
            return False
        
        target_stage = stage_mapping[current_state]
        
        # Update assignee using PATCH method
        update_url = f'{confcwUrl}/{page_id}/approvals/assign?expand=state,states,actions,approvals&admin=true'
        
        assignee_data = {
            'name': target_stage,
            'assignees': [{'username': jira_assignee}] if jira_assignee else []
        }
        
        update_response = requests.patch(
            update_url,
            data=json.dumps(assignee_data),
            auth=HTTPBasicAuth(username, password),
            headers={'Content-Type': 'application/json'}
        )
        
        if update_response.status_code == 200:
            print(f'✅ Page {page_id}: Updated assignee to "{jira_assignee}"')
            return True
        else:
            print(f'❌ Page {page_id}: Failed to update assignee - {update_response.status_code}')
            return False
            
    except Exception as e:
        print(f'❌ Page {page_id}: Error updating assignee - {str(e)}')
        return False

# ============================================================================
# 7. REMOVE OLD PERMISSIONS
# ============================================================================

def removeuser(user, page_id):
    """Remove user permission from Confluence page"""
    
    if not user or user.strip() == '':
        return ''
    
    url = f'https://gnanetra.iyc.ishafoundation.org/ArApi/conf/rm-perm?pid={page_id}&user={user}'
    
    try:
        resp = requests.post(url, timeout=30)
        if resp.status_code == 200:
            print(f'🗑️ Page {page_id}: Removed permission for "{user}"')
            return user
        else:
            print(f'⚠️ Page {page_id}: Could not remove permission for "{user}" - Status {resp.status_code}')
            return 'error'
    except Exception as e:
        print(f'❌ Page {page_id}: Error removing permission for "{user}" - {str(e)}')
        return 'error'

def remove_old_assignee_permission(page_id, old_assignee):
    """Remove permission for old assignee"""
    
    if not old_assignee:
        return ''
    
    result = removeuser(old_assignee, page_id)
    return result if result != 'error' else ''

# ============================================================================
# 8. MAIN PROCESSING FUNCTION
# ============================================================================

def process_jira_issue(issue):
    """Process a single JIRA issue for assignee sync"""
    global totalupdates
    
    jira_key = issue['key']
    jira_assignee = issue['fields']['assignee']['name'] if issue['fields']['assignee'] else ''
    confluence_link = issue['fields'].get('customfield_111118')
    
    print(f"\n🔄 Processing {jira_key}")
    print(f"   JIRA Assignee: '{jira_assignee}'")
    
    # Step 3: Find matching Confluence page
    page_id = get_confluence_page_id(jira_key, confluence_link)
    if not page_id:
        return
    
    # Step 4: Read Confluence current state
    confluence_assignee = get_confluence_assignee(page_id)
    if confluence_assignee is None:
        return
    
    print(f"   Confluence Assignee: '{confluence_assignee}'")
    
    # Step 5: Compare assignees
    if assignees_match(jira_assignee, confluence_assignee):
        print(f"✅ {jira_key}: Assignees already match - no update needed")
        return
    
    print(f"🔄 {jira_key}: Assignee mismatch detected - syncing...")
    
    # Step 6: Update Confluence assignee
    if update_confluence_assignee(page_id, jira_assignee):
        totalupdates += 1
        
        # Step 7: Remove old permissions
        if confluence_assignee and confluence_assignee != jira_assignee:
            removed = remove_old_assignee_permission(page_id, confluence_assignee)
            if removed:
                print(f"🔑 {jira_key}: Removed old assignee '{confluence_assignee}'")
        
        print(f"🎯 {jira_key}: Successfully synced assignee")
    else:
        print(f"❌ {jira_key}: Failed to sync assignee")

# ============================================================================
# MAIN EXECUTION
# ============================================================================

def main():
    """Main execution function"""
    global totalupdates
    totalupdates = 0
    
    print("=" * 70)
    print("🚀 JIRA-CONFLUENCE ASSIGNEE SYNC")
    print("=" * 70)
    
    try:
        # Step 2: Fetch JIRA tickets
        jira_issues = fetch_jira_tickets()
        
        if len(jira_issues) == 0:
            print("✅ No issues to process")
            return
        
        print(f"\n📝 Processing {len(jira_issues)} issues...")
        
        # Process each issue
        for issue in jira_issues:
            try:
                process_jira_issue(issue)
            except Exception as e:
                print(f"❌ Error processing {issue['key']}: {str(e)}")
    
    except Exception as e:
        print(f"❌ Error in main execution: {str(e)}")
    
    # Final summary
    print("\n" + "=" * 70)
    print(f"🎯 SYNC COMPLETE - Total Updates: {totalupdates}")
    print("=" * 70)

def log_execution():
    """Log script execution"""
    run_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    try:
        with open('assignee-sync-log.txt', 'a', encoding='utf-8') as f:
            f.write(f'Assignee sync ran: {run_time} - Updates: {totalupdates}\n')
    except Exception as e:
        print(f"Could not write to log file: {str(e)}")

# ============================================================================
# SCRIPT ENTRY POINT
# ============================================================================

if __name__ == "__main__":
    main()
    log_execution()
