import requests
from requests.auth import HTTPBasicAuth
import json
import time
import datetime
import base64
import urllib.parse
import os

# ============================================================================
# 1. ENHANCED INITIALIZATION & SETUP ⚙️
# ============================================================================

# Global variables
totalupdates = 0
CACHE_FILE = 'jira_assignee_history.json'

# Time range for processing (last 1 minute)
time_now = datetime.datetime.now().strftime("%Y-%m-%d %H:%M")
time_one_minute_ago = (datetime.datetime.now() - datetime.timedelta(minutes=1)).strftime("%Y-%m-%d %H:%M")

# Confluence configuration
username = 'script'
password = 'createpage'
confUrl = 'https://art.iyc.ishafoundation.org'

# JIRA configuration
jira_url = "https://servicedesk.isha.in"
decoded_str = base64.b64decode('YXJjaGl2ZXMuc2NyaXB0OkB1dG9tYXRl').decode('utf-8')
Cusername, Cpassword = decoded_str.split(":")
auth = HTTPBasicAuth(Cusername, Cpassword)
api_url = f"{jira_url}/rest/api/2/search"

# Enhanced session configuration
session = requests.Session()
session.timeout = 30
adapter = requests.adapters.HTTPAdapter(max_retries=3)
session.mount('http://', adapter)
session.mount('https://', adapter)

print("🚀 JIRA-Confluence Permission Sync (Enhanced Logic)")
print(f"⏰ Processing updates from {time_one_minute_ago} to {time_now}")

# ============================================================================
# CACHE MANAGEMENT FOR TRACKING PREVIOUS ASSIGNEES
# ============================================================================

def load_assignee_history():
    """Load previous JIRA assignee history from cache"""
    if os.path.exists(CACHE_FILE):
        try:
            with open(CACHE_FILE, 'r', encoding='utf-8') as f:
                history = json.load(f)
                print(f"📋 Loaded assignee history for {len(history)} issues")
                return history
        except Exception as e:
            print(f"⚠️ Could not load assignee history: {str(e)}")
            return {}
    else:
        print("📋 No assignee history found, starting fresh")
        return {}

def save_assignee_history(history):
    """Save JIRA assignee history to cache"""
    try:
        with open(CACHE_FILE, 'w', encoding='utf-8') as f:
            json.dump(history, f, indent=2)
        print(f"💾 Saved assignee history for {len(history)} issues")
    except Exception as e:
        print(f"⚠️ Could not save assignee history: {str(e)}")

# ============================================================================
# 2. ENHANCED JIRA TICKET FETCHING 📋
# ============================================================================

def fetch_jira_tickets():
    """Fetch JIRA tickets with improved error handling"""
    
    query = f'''project = "Ar Transcription" 
                AND type = "Not Transcribed Contents" 
                AND updated >= "{time_one_minute_ago}" 
                AND updated < "{time_now}"'''
    
    print(f"📋 JQL Query: {query}")
    
    all_issues = []
    max_results = 100  # Smaller batches for better performance
    start_at = 0
    
    while True:
        params = {
            'jql': query,
            'startAt': start_at,
            'maxResults': max_results,
            'fields': 'key,summary,assignee,customfield_111118'
        }
        
        # Improved retry mechanism
        max_tries = 5
        tries = 0
        
        while tries < max_tries:
            try:
                response = session.get(api_url, params=params, auth=auth)
                
                if response.status_code == 200:
                    break
                elif response.status_code == 400:
                    print(f"❌ JQL Syntax Error: {response.text}")
                    return []
                else:
                    tries += 1
                    wait_time = min(2 ** tries, 10)  # Exponential backoff, max 10s
                    print(f'⚠️ JIRA API retry {tries}/{max_tries} (waiting {wait_time}s)')
                    time.sleep(wait_time)
                    
            except Exception as e:
                tries += 1
                print(f'⚠️ JIRA API error (attempt {tries}): {str(e)}')
                if tries < max_tries:
                    time.sleep(2)
        
        if tries >= max_tries:
            print("❌ Failed to fetch JIRA issues after retries")
            break
            
        data = response.json()
        all_issues.extend(data['issues'])
        
        if start_at + max_results >= data['total']:
            break
        start_at += max_results
    
    print(f"📊 Fetched {len(all_issues)} JIRA issues")
    return all_issues

# ============================================================================
# 3. IMPROVED PAGE ID EXTRACTION
# ============================================================================

def get_confluence_page_id(jira_key, confluence_link):
    """Extract page_id with better error handling"""
    
    if not confluence_link or confluence_link.strip() == '':
        print(f'⚠️ {jira_key}: No Confluence link in customfield_111118')
        return None
    
    if isinstance(confluence_link, str):
        title = confluence_link.split('/')[-1].strip()
    else:
        print(f'⚠️ {jira_key}: Invalid Confluence link format')
        return None
    
    if not title:
        print(f'⚠️ {jira_key}: Could not extract page title')
        return None
    
    print(f"📄 {jira_key}: Page title: '{title}'")
    
    encoded_title = urllib.parse.quote(title)
    url = f'{confUrl}/rest/api/content/?title={encoded_title}'
    
    # Try multiple times with different encodings if needed
    for attempt in range(2):
        try:
            response = requests.get(url, auth=HTTPBasicAuth(username, password), timeout=30)
            
            if response.status_code == 200:
                page_data = response.json()
                if 'results' in page_data and len(page_data['results']) > 0:
                    page_id = page_data['results'][0]['id']
                    print(f'✅ {jira_key}: Found page_id {page_id}')
                    return page_id
            
            if attempt == 0:
                # Try with simple title if encoded version fails
                url = f'{confUrl}/rest/api/content/?title={title}'
            
        except Exception as e:
            print(f'❌ {jira_key}: Error finding page (attempt {attempt + 1}) - {str(e)}')
    
    print(f'❌ {jira_key}: Page "{title}" not found after all attempts')
    return None

# ============================================================================
# 4. SMART PERMISSION MANAGEMENT LOGIC
# ============================================================================

def grant_access_to_user(page_id, username):
    """Grant access with improved error feedback"""
    
    if not username or username.strip() == '':
        return True
    
    url = f'https://gnanetra.iyc.ishafoundation.org/ArApi/conf/add-perm?pid={page_id}&user={username}'
    
    try:
        resp = requests.post(url, timeout=30)
        
        if resp.status_code == 200:
            print(f'✅ Granted access to "{username}"')
            return True
        elif resp.status_code == 409:
            print(f'ℹ️ User "{username}" already has access')
            return True
        else:
            print(f'❌ Failed to grant access to "{username}" - Status: {resp.status_code}')
            if resp.text:
                print(f'   Response: {resp.text}')
            return False
            
    except Exception as e:
        print(f'❌ Error granting access to "{username}": {str(e)}')
        return False

def remove_access_from_user(page_id, username):
    """Remove access with improved error feedback"""
    
    if not username or username.strip() == '':
        return True
    
    url = f'https://gnanetra.iyc.ishafoundation.org/ArApi/conf/rm-perm?pid={page_id}&user={username}'
    
    try:
        resp = requests.post(url, timeout=30)
        
        if resp.status_code == 200:
            print(f'🗑️ Removed access from "{username}"')
            return True
        elif resp.status_code == 404:
            print(f'ℹ️ User "{username}" already removed or not found')
            return True
        else:
            print(f'⚠️ Could not remove access from "{username}" - Status: {resp.status_code}')
            return False
            
    except Exception as e:
        print(f'❌ Error removing access from "{username}": {str(e)}')
        return False

# ============================================================================
# 5. SMART PROCESSING LOGIC
# ============================================================================

def process_jira_issue(issue, assignee_history):
    """Enhanced processing with assignee change detection"""
    global totalupdates
    
    jira_key = issue['key']
    current_assignee = issue['fields']['assignee']['name'] if issue['fields']['assignee'] else ''
    confluence_link = issue['fields'].get('customfield_111118')
    
    print(f"\n🔄 Processing {jira_key}")
    print(f"   Current JIRA Assignee: '{current_assignee}'")
    
    # Get previous assignee from history
    previous_assignee = assignee_history.get(jira_key, '')
    print(f"   Previous JIRA Assignee: '{previous_assignee}'")
    
    # OPTIMIZATION: Skip if no assignee change
    if current_assignee == previous_assignee:
        print(f"✅ {jira_key}: No assignee change - skipping permission sync")
        return
    
    # Get Confluence page_id
    page_id = get_confluence_page_id(jira_key, confluence_link)
    if not page_id:
        return
    
    changes_made = False
    
    # Grant access to current assignee
    if current_assignee:
        if grant_access_to_user(page_id, current_assignee):
            changes_made = True
        else:
            print(f"❌ {jira_key}: Failed to grant access, skipping removal")
            return
    
    # IMPROVED LOGIC: Remove access ONLY from previous JIRA assignee
    if previous_assignee and previous_assignee != current_assignee:
        if remove_access_from_user(page_id, previous_assignee):
            changes_made = True
        else:
            print(f"⚠️ {jira_key}: Could not remove previous assignee, but current assignee was granted access")
    
    # Update history
    assignee_history[jira_key] = current_assignee
    
    if changes_made:
        totalupdates += 1
        print(f"🎯 {jira_key}: Permission sync completed successfully")
    else:
        print(f"⚠️ {jira_key}: No permission changes were made")

# ============================================================================
# 6. ENHANCED MAIN EXECUTION
# ============================================================================

def main():
    """Enhanced main execution with better error isolation"""
    global totalupdates
    totalupdates = 0
    
    print("=" * 80)
    print("🚀 JIRA-CONFLUENCE PERMISSION SYNC - ENHANCED LOGIC")
    print("=" * 80)
    
    try:
        # Load assignee history
        assignee_history = load_assignee_history()
        
        # Fetch JIRA tickets
        jira_issues = fetch_jira_tickets()
        
        if len(jira_issues) == 0:
            print("✅ No issues to process")
            return
        
        print(f"\n📝 Processing {len(jira_issues)} issues...")
        
        # Process each issue with error isolation
        successful_processes = 0
        failed_processes = 0
        
        for i, issue in enumerate(jira_issues, 1):
            try:
                print(f"\n📍 [{i}/{len(jira_issues)}]", end=" ")
                process_jira_issue(issue, assignee_history)
                successful_processes += 1
                
                # Small delay to prevent overwhelming APIs
                time.sleep(0.1)
                
            except Exception as e:
                failed_processes += 1
                print(f"❌ Error processing {issue.get('key', 'unknown')}: {str(e)}")
                continue
        
        # Save updated assignee history
        save_assignee_history(assignee_history)
        
        # Enhanced summary
        print(f"\n📊 Processing Summary:")
        print(f"   ✅ Successful: {successful_processes}")
        print(f"   ❌ Failed: {failed_processes}")
        print(f"   🔄 Permission Updates: {totalupdates}")
    
    except Exception as e:
        print(f"❌ Critical error in main execution: {str(e)}")
    
    print("\n" + "=" * 80)
    print(f"🎯 SYNC COMPLETE - Total Permission Updates: {totalupdates}")
    print("=" * 80)

def log_execution():
    """Enhanced logging with more details"""
    run_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    try:
        with open('permission-sync-log.txt', 'a', encoding='utf-8') as f:
            f.write(f'{run_time} - Permission sync completed - Updates: {totalupdates}\n')
    except Exception as e:
        print(f"Could not write to log file: {str(e)}")

# ============================================================================
# SCRIPT ENTRY POINT
# ============================================================================

if __name__ == "__main__":
    main()
    log_execution()
