import requests
from requests.auth import HTTPBasicAuth
import json
import time
import datetime
import base64
import urllib.parse
import os

# ============================================================================
# 🔧 USERNAME MAPPING CONFIGURATION
# ============================================================================

# Map JIRA usernames to Confluence usernames when they differ
USERNAME_MAPPING = {
    'yogesh.ramprabhu': 'yogesh ramprabhu',  # Update this with correct Confluence username
    'rohit.dutt': 'Rohit Dutt', 
    'Ancy Ravindra': 'Ancy Ravindra',# Example: common format difference
    # Add more mappings as discovered:
    # 'jira.username': 'confluence.username',
}

def get_confluence_username(jira_username):
    """Convert JIRA username to Confluence username"""
    if not jira_username:
        return jira_username
    
    confluence_username = USERNAME_MAPPING.get(jira_username, jira_username)
    if confluence_username != jira_username:
        print(f"🔄 Username mapping: '{jira_username}' → '{confluence_username}'")
    return confluence_username

# ============================================================================
# 1. INITIALIZATION & SETUP ⚙️
# ============================================================================

totalupdates = 0
CACHE_FILE = 'jira_assignee_cache.json'

# Time range (last 30 seconds)
time_now = datetime.datetime.now().strftime("%Y-%m-%d %H:%M")
time_thirty_seconds_ago = (datetime.datetime.now() - datetime.timedelta(seconds=30)).strftime("%Y-%m-%d %H:%M")

# URLs and credentials
confUrl = 'https://art.iyc.ishafoundation.org'
jira_url = "https://servicedesk.isha.in"
api_url = f"{jira_url}/rest/api/2/search"

username = 'script'
password = 'createpage'
decoded_str = base64.b64decode('YXJjaGl2ZXMuc2NyaXB0OkB1dG9tYXRl').decode('utf-8')
Cusername, Cpassword = decoded_str.split(":")
auth = HTTPBasicAuth(Cusername, Cpassword)

session = requests.Session()
session.timeout = 30

print("🚀 JIRA-Confluence Permission Sync (Production Version)")
print(f"⏰ Processing updates from {time_thirty_seconds_ago} to {time_now}")

# Cache functions
def load_previous_assignees():
    if os.path.exists(CACHE_FILE):
        try:
            with open(CACHE_FILE, 'r', encoding='utf-8') as f:
                cache = json.load(f)
                print(f"📋 Loaded {len(cache)} cached assignees")
                return cache
        except:
            print("⚠️ Could not load cache, starting fresh")
            return {}
    return {}

def save_previous_assignees(cache):
    try:
        with open(CACHE_FILE, 'w', encoding='utf-8') as f:
            json.dump(cache, f, indent=2)
        print(f"💾 Saved {len(cache)} assignees to cache")
    except Exception as e:
        print(f"⚠️ Could not save cache: {str(e)}")

# ============================================================================
# 2. FETCH JIRA TICKETS (LAST 30 SECONDS) 📋
# ============================================================================

def fetch_jira_tickets():
    query = f'project = "Ar Transcription" AND type = "Not Transcribed Contents" AND updated >= "{time_thirty_seconds_ago}" AND updated < "{time_now}"'
    print(f"📋 JQL Query: {query}")
    
    all_issues = []
    max_results = 500
    start_at = 0
    
    while True:
        params = {
            'jql': query,
            'startAt': start_at,
            'maxResults': max_results,
            'fields': 'key,summary,assignee,customfield_111118'
        }
        
        max_tries = 3
        for attempt in range(max_tries):
            try:
                response = session.get(api_url, params=params, auth=auth)
                if response.status_code == 200:
                    break
                elif response.status_code == 400:
                    print(f"❌ JQL Error: {response.text}")
                    return []
                else:
                    print(f"⚠️ JIRA API retry {attempt + 1}/{max_tries}")
                    time.sleep(1)
            except Exception as e:
                print(f"⚠️ JIRA API error: {str(e)}")
                if attempt < max_tries - 1:
                    time.sleep(1)
        else:
            print("❌ Failed to fetch JIRA issues")
            return []
            
        data = response.json()
        all_issues.extend(data['issues'])
        
        if start_at + max_results >= data['total']:
            break
        start_at += max_results
    
    print(f"📊 Fetched {len(all_issues)} JIRA issues")
    return all_issues

# ============================================================================
# 3. EXTRACT PAGE TITLE & GET PAGE_ID
# ============================================================================

def get_confluence_page_id(jira_key, confluence_link):
    if not confluence_link or confluence_link.strip() == '':
        print(f'⚠️ {jira_key}: No Confluence link')
        return None
    
    title = confluence_link.split('/')[-1].strip() if isinstance(confluence_link, str) else None
    if not title:
        print(f'⚠️ {jira_key}: Could not extract page title')
        return None
    
    print(f"📄 {jira_key}: Page title: '{title}'")
    
    encoded_title = urllib.parse.quote(title)
    url = f'{confUrl}/rest/api/content/?title={encoded_title}'
    
    try:
        response = requests.get(url, auth=HTTPBasicAuth(username, password), timeout=30)
        if response.status_code != 200:
            print(f'❌ {jira_key}: Confluence search failed ({response.status_code})')
            return None
        
        page_data = response.json()
        if 'results' not in page_data or len(page_data['results']) == 0:
            print(f'❌ {jira_key}: Page not found')
            return None
        
        page_id = page_data['results'][0]['id']
        print(f'✅ {jira_key}: Found page_id {page_id}')
        return page_id
        
    except Exception as e:
        print(f'❌ {jira_key}: Error finding page - {str(e)}')
        return None

# ============================================================================
# 4. PERMISSION MANAGEMENT LOGIC
# ============================================================================

def grant_access_to_user(page_id, jira_username):
    """Grant Confluence access with username mapping"""
    if not jira_username:
        return True
    
    confluence_username = get_confluence_username(jira_username)
    url = f'https://gnanetra.iyc.ishafoundation.org/ArApi/conf/add-perm?pid={page_id}&user={confluence_username}'
    
    try:
        response = requests.post(url, timeout=30)
        if response.status_code == 200:
            print(f'✅ Granted access: "{confluence_username}"')
            return True
        else:
            print(f'❌ Failed to grant access to "{confluence_username}" (Status: {response.status_code})')
            if response.status_code == 404:
                print(f'   💡 Add to USERNAME_MAPPING: \'{jira_username}\': \'correct_confluence_username\'')
            return False
    except Exception as e:
        print(f'❌ Error granting access to "{confluence_username}": {str(e)}')
        return False

def remove_access_from_user(page_id, jira_username):
    """Remove Confluence access with username mapping"""
    if not jira_username:
        return True
    
    confluence_username = get_confluence_username(jira_username)
    url = f'https://gnanetra.iyc.ishafoundation.org/ArApi/conf/rm-perm?pid={page_id}&user={confluence_username}'
    
    try:
        response = requests.post(url, timeout=30)
        if response.status_code == 200:
            print(f'🗑️ Removed access: "{confluence_username}"')
            return True
        else:
            print(f'⚠️ Could not remove access from "{confluence_username}" (Status: {response.status_code})')
            return False
    except Exception as e:
        print(f'❌ Error removing access from "{confluence_username}": {str(e)}')
        return False

def sync_permissions(issue, assignee_cache):
    """Main permission sync logic"""
    global totalupdates
    
    jira_key = issue['key']
    current_assignee = issue['fields']['assignee']['name'] if issue['fields']['assignee'] else ''
    confluence_link = issue['fields'].get('customfield_111118')
    
    print(f"\n🔄 Processing {jira_key}")
    print(f"   Current: '{current_assignee}'")
    
    previous_assignee = assignee_cache.get(jira_key, '')
    print(f"   Previous: '{previous_assignee}'")
    
    # Skip if no change
    if current_assignee == previous_assignee:
        print(f"✅ {jira_key}: No assignee change")
        return
    
    # Get Confluence page
    page_id = get_confluence_page_id(jira_key, confluence_link)
    if not page_id:
        return
    
    changes_made = False
    
    # Grant access to current assignee
    if current_assignee:
        if grant_access_to_user(page_id, current_assignee):
            changes_made = True
    
    # Remove access from previous assignee
    if previous_assignee and previous_assignee != current_assignee:
        if remove_access_from_user(page_id, previous_assignee):
            changes_made = True
    
    # Update cache
    assignee_cache[jira_key] = current_assignee
    
    if changes_made:
        totalupdates += 1
        print(f"🎯 {jira_key}: Permissions synced")
    else:
        print(f"⚠️ {jira_key}: No changes applied")

# ============================================================================
# MAIN EXECUTION
# ============================================================================

def main():
    global totalupdates
    totalupdates = 0
    
    print("=" * 80)
    print("🚀 JIRA-CONFLUENCE PERMISSION SYNC")
    print("=" * 80)
    
    try:
        # Load cache and fetch issues
        assignee_cache = load_previous_assignees()
        jira_issues = fetch_jira_tickets()
        
        if len(jira_issues) == 0:
            print("✅ No issues to process")
            return
        
        print(f"\n📝 Processing {len(jira_issues)} issues...\n")
        
        # Process each issue
        for i, issue in enumerate(jira_issues, 1):
            try:
                print(f"📍 [{i}/{len(jira_issues)}]", end=" ")
                sync_permissions(issue, assignee_cache)
            except Exception as e:
                print(f"❌ Error processing {issue.get('key', 'unknown')}: {str(e)}")
                continue
        
        # Save cache
        save_previous_assignees(assignee_cache)
        
    except Exception as e:
        print(f"❌ Critical error: {str(e)}")
    
    print("\n" + "=" * 80)
    print(f"🎯 SYNC COMPLETE - Total Updates: {totalupdates}")
    print("=" * 80)

def log_execution():
    run_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    try:
        with open('sync_log.txt', 'a', encoding='utf-8') as f:
            f.write(f'{run_time} - Permission sync completed - Updates: {totalupdates}\n')
    except:
        pass

if __name__ == "__main__":
    main()
    log_execution()
