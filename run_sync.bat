@echo off
echo ================================================================================
echo 🚀 JIRA-Confluence Permission Sync
echo ================================================================================
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python is not installed or not in PATH
    echo Please install Python 3.6+ and try again
    pause
    exit /b 1
)

REM Check if app.py exists
if not exist "app.py" (
    echo ❌ app.py not found in current directory
    echo Please ensure you're running this from the correct folder
    pause
    exit /b 1
)

REM Show menu
echo Choose an option:
echo 1. Run normal sync
echo 2. Run comprehensive tests
echo 3. Test specific tickets
echo 4. Validate configuration
echo 5. Exit
echo.
set /p choice="Enter your choice (1-5): "

if "%choice%"=="1" (
    echo.
    echo 🔄 Running normal sync...
    python app.py
) else if "%choice%"=="2" (
    echo.
    echo 🧪 Running comprehensive tests...
    python app.py test
) else if "%choice%"=="3" (
    echo.
    echo 🎯 Testing specific tickets...
    python app.py test-tickets
) else if "%choice%"=="4" (
    echo.
    echo 🔧 Validating configuration...
    python validate_config.py
) else if "%choice%"=="5" (
    echo.
    echo 👋 Goodbye!
    exit /b 0
) else (
    echo.
    echo ❌ Invalid choice. Please try again.
)

echo.
echo ================================================================================
echo ✅ Operation completed. Check the output above for results.
echo ================================================================================
pause
