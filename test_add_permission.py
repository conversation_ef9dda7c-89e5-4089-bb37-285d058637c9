#!/usr/bin/env python3
"""
🔧 TEST ADD PERMISSION API
Find the working ADD permission endpoint
"""

import requests
from requests.auth import HTTPBasicAuth
import json
from datetime import datetime

def test_add_permission_variations():
    """Test different variations of the ADD permission API"""
    print("🔧 TESTING ADD PERMISSION API VARIATIONS")
    print("="*60)
    
    # Use Confluence credentials
    username = 'script'
    password = 'createpage'
    auth = HTTPBasicAuth(username, password)
    
    page_id = "151139217"
    test_user = "ancy.ravindra"
    
    # Test different endpoint variations
    endpoints = [
        "https://gnanetra.iyc.ishafoundation.org/ArApi/conf/add-perm",
        "https://gnanetra.iyc.ishafoundation.org/ArApi/conf/addperm",
        "https://gnanetra.iyc.ishafoundation.org/ArApi/conf/add_perm",
        "https://gnanetra.iyc.ishafoundation.org/ArApi/conf/grant",
        "https://gnanetra.iyc.ishafoundation.org/ArApi/conf/grant-perm",
        "https://gnanetra.iyc.ishafoundation.org/ArApi/add-perm",
        "https://gnanetra.iyc.ishafoundation.org/ArApi/addperm",
        "https://gnanetra.iyc.ishafoundation.org/ArApi/grant",
        "https://art.iyc.ishafoundation.org/ArApi/add-perm",
        "https://art.iyc.ishafoundation.org/ArApi/addperm",
    ]
    
    for endpoint in endpoints:
        print(f"\n🔗 Testing: {endpoint}")
        
        # Test with different parameter formats
        param_formats = [
            f"?pid={page_id}&user={test_user}",
            f"?page_id={page_id}&username={test_user}",
            f"?id={page_id}&user={test_user}",
            f"?pageid={page_id}&user={test_user}",
        ]
        
        for params in param_formats:
            url = endpoint + params
            
            try:
                # Try POST method
                response = requests.post(url, auth=auth, timeout=10)
                if response.status_code == 200:
                    print(f"   ✅ SUCCESS POST: {url}")
                    print(f"   📝 Response: {response.text}")
                    return url  # Return the working URL
                elif response.status_code != 404:
                    print(f"   ⚠️ POST {response.status_code}: {url}")
                    if response.text and len(response.text) < 100:
                        print(f"   📝 Response: {response.text}")
                
                # Try GET method if POST doesn't work
                response = requests.get(url, auth=auth, timeout=10)
                if response.status_code == 200:
                    print(f"   ✅ SUCCESS GET: {url}")
                    print(f"   📝 Response: {response.text}")
                    return url  # Return the working URL
                elif response.status_code != 404:
                    print(f"   ⚠️ GET {response.status_code}: {url}")
                    
            except Exception as e:
                print(f"   ❌ Error: {str(e)}")
    
    return None

def test_with_existing_user():
    """Test with the user that already has access"""
    print("\n🔧 TESTING WITH EXISTING USER")
    print("="*60)
    
    username = 'script'
    password = 'createpage'
    auth = HTTPBasicAuth(username, password)
    
    page_id = "151139217"
    existing_user = "satyasree.a"  # User who already has access
    
    # Test remove first (we know this works)
    remove_url = f"https://gnanetra.iyc.ishafoundation.org/ArApi/conf/rm-perm?pid={page_id}&user={existing_user}"
    
    try:
        response = requests.post(remove_url, auth=auth, timeout=30)
        print(f"🗑️ Remove existing user: {response.status_code}")
        print(f"   Response: {response.text}")
        
        if response.status_code == 200:
            print("✅ Successfully removed existing user")
            
            # Now try to add them back with different endpoints
            add_endpoints = [
                f"https://gnanetra.iyc.ishafoundation.org/ArApi/conf/add-perm?pid={page_id}&user={existing_user}",
                f"https://gnanetra.iyc.ishafoundation.org/ArApi/add-perm?pid={page_id}&user={existing_user}",
                f"https://art.iyc.ishafoundation.org/ArApi/add-perm?pid={page_id}&user={existing_user}",
            ]
            
            for add_url in add_endpoints:
                try:
                    response = requests.post(add_url, auth=auth, timeout=30)
                    print(f"➕ Add back user ({add_url}): {response.status_code}")
                    if response.text:
                        print(f"   Response: {response.text}")
                    
                    if response.status_code == 200:
                        print(f"✅ SUCCESS! Working add endpoint: {add_url}")
                        return add_url
                        
                except Exception as e:
                    print(f"   ❌ Error: {str(e)}")
        
    except Exception as e:
        print(f"❌ Error with existing user test: {str(e)}")
    
    return None

def test_standard_confluence_restrictions():
    """Test using standard Confluence API to manage restrictions"""
    print("\n🔧 TESTING STANDARD CONFLUENCE RESTRICTIONS API")
    print("="*60)
    
    confUrl = 'https://art.iyc.ishafoundation.org'
    username = 'script'
    password = 'createpage'
    auth = HTTPBasicAuth(username, password)
    
    page_id = "151139217"
    test_user = "ancy.ravindra"
    
    # Try to update restrictions using standard API
    try:
        # First get current restrictions
        url = f"{confUrl}/rest/api/content/{page_id}?expand=restrictions.read.restrictions.user"
        response = requests.get(url, auth=auth, timeout=30)
        
        if response.status_code == 200:
            page_data = response.json()
            current_restrictions = page_data.get('restrictions', {}).get('read', {}).get('restrictions', {}).get('user', {}).get('results', [])
            
            print(f"📋 Current users with access: {len(current_restrictions)}")
            for user in current_restrictions:
                print(f"   👤 {user.get('username', 'Unknown')} ({user.get('displayName', 'Unknown')})")
            
            # Add our test user to the list
            new_user = {
                "type": "known",
                "username": test_user
            }
            
            # Create new restriction data
            restriction_data = {
                "operation": "read",
                "restrictions": {
                    "user": current_restrictions + [new_user]
                }
            }
            
            # Try to update restrictions
            update_url = f"{confUrl}/rest/api/content/{page_id}/restriction"
            headers = {'Content-Type': 'application/json'}
            
            response = requests.put(update_url, 
                                  json=restriction_data, 
                                  auth=auth, 
                                  headers=headers,
                                  timeout=30)
            
            print(f"📤 Update restrictions: {response.status_code}")
            if response.text:
                print(f"   Response: {response.text[:300]}")
            
            if response.status_code in [200, 201]:
                print("✅ Successfully updated restrictions via standard API!")
                return True
        
    except Exception as e:
        print(f"❌ Error with standard API: {str(e)}")
    
    return False

def main():
    """Test all approaches to find working ADD permission method"""
    print("🔧 COMPREHENSIVE ADD PERMISSION TESTING")
    print("🕐 Started at:", datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
    
    # Test 1: Try different endpoint variations
    working_url = test_add_permission_variations()
    if working_url:
        print(f"\n🎉 FOUND WORKING ADD ENDPOINT: {working_url}")
        return
    
    # Test 2: Test with existing user
    working_url = test_with_existing_user()
    if working_url:
        print(f"\n🎉 FOUND WORKING ADD ENDPOINT: {working_url}")
        return
    
    # Test 3: Try standard Confluence API
    if test_standard_confluence_restrictions():
        print(f"\n🎉 STANDARD CONFLUENCE API WORKS!")
        return
    
    print("\n❌ No working ADD permission method found")
    print("🔍 However, we confirmed REMOVE works: https://gnanetra.iyc.ishafoundation.org/ArApi/conf/rm-perm")
    
    print("\n" + "="*60)
    print("🎯 ADD PERMISSION TESTING COMPLETE")
    print("="*60)

if __name__ == "__main__":
    main()
