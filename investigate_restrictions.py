#!/usr/bin/env python3
"""
🔍 INVESTIGATE RESTRICTIONS
Deep investigation of why users don't appear in page restrictions
"""

import requests
from requests.auth import HTTPBasicAuth
import json
from datetime import datetime

def investigate_page_restrictions():
    """Investigate the page restrictions in detail"""
    print("🔍 INVESTIGATING PAGE RESTRICTIONS")
    print("="*60)
    
    confUrl = 'https://art.iyc.ishafoundation.org'
    conf_username = 'script'
    conf_password = 'createpage'
    auth = HTTPBasicAuth(conf_username, conf_password)
    
    page_id = "151139217"
    
    # Get full page details
    try:
        url = f"{confUrl}/rest/api/content/{page_id}?expand=restrictions,space,ancestors"
        response = requests.get(url, auth=auth, timeout=30)
        
        if response.status_code == 200:
            page_data = response.json()
            
            print(f"📄 Page Title: {page_data.get('title', 'Unknown')}")
            print(f"📚 Space: {page_data.get('space', {}).get('name', 'Unknown')}")
            print(f"🆔 Page ID: {page_data.get('id', 'Unknown')}")
            print(f"📊 Status: {page_data.get('status', 'Unknown')}")
            
            # Check restrictions in detail
            restrictions = page_data.get('restrictions', {})
            print(f"\n🔒 RESTRICTIONS ANALYSIS:")
            print(f"   Raw restrictions: {json.dumps(restrictions, indent=2)}")
            
            # Check if page has any restrictions at all
            if not restrictions or not restrictions.get('read'):
                print(f"   ⚠️ Page has NO READ RESTRICTIONS configured")
                print(f"   💡 This means the page is open to all space users")
                
                # Check space permissions
                space_key = page_data.get('space', {}).get('key', '')
                if space_key:
                    check_space_permissions(confUrl, auth, space_key)
            else:
                print(f"   ✅ Page has read restrictions configured")
                
                read_restrictions = restrictions.get('read', {}).get('restrictions', {})
                
                # Check user restrictions
                user_restrictions = read_restrictions.get('user', {})
                if user_restrictions:
                    users = user_restrictions.get('results', [])
                    print(f"   👥 Users with access: {len(users)}")
                    for user in users:
                        print(f"      👤 {user.get('username')} ({user.get('displayName')})")
                else:
                    print(f"   ⚠️ No user-specific restrictions found")
                
                # Check group restrictions
                group_restrictions = read_restrictions.get('group', {})
                if group_restrictions:
                    groups = group_restrictions.get('results', [])
                    print(f"   👥 Groups with access: {len(groups)}")
                    for group in groups:
                        print(f"      👥 {group.get('name')}")
                else:
                    print(f"   ⚠️ No group-specific restrictions found")
        else:
            print(f"❌ Could not get page details: {response.status_code}")
            print(f"   Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Error investigating page: {str(e)}")

def check_space_permissions(confUrl, auth, space_key):
    """Check space-level permissions"""
    print(f"\n🏢 SPACE PERMISSIONS ANALYSIS:")
    
    try:
        url = f"{confUrl}/rest/api/space/{space_key}?expand=permissions"
        response = requests.get(url, auth=auth, timeout=30)
        
        if response.status_code == 200:
            space_data = response.json()
            print(f"   📚 Space: {space_data.get('name', 'Unknown')}")
            print(f"   🔑 Key: {space_data.get('key', 'Unknown')}")
            
            permissions = space_data.get('permissions', [])
            print(f"   🔒 Space permissions: {len(permissions)}")
            
            for perm in permissions[:5]:  # Show first 5
                print(f"      🔐 {perm.get('operation', 'Unknown')}: {perm.get('anonymousAccess', False)}")
        else:
            print(f"   ❌ Could not get space permissions: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Error checking space permissions: {str(e)}")

def test_different_permission_approaches():
    """Test different approaches to set page permissions"""
    print(f"\n🧪 TESTING DIFFERENT PERMISSION APPROACHES")
    print("="*60)
    
    confUrl = 'https://art.iyc.ishafoundation.org'
    conf_username = 'script'
    conf_password = 'createpage'
    auth = HTTPBasicAuth(conf_username, conf_password)
    
    page_id = "151139217"
    test_user = "rohit.dutt"
    
    # Approach 1: Standard Confluence REST API
    print(f"1️⃣ Testing Standard Confluence REST API")
    test_standard_api_restrictions(confUrl, auth, page_id, test_user)
    
    # Approach 2: Our current API
    print(f"\n2️⃣ Testing Current Permission API")
    test_current_api(page_id, test_user)
    
    # Approach 3: Check if we need to create restrictions first
    print(f"\n3️⃣ Testing Restriction Creation")
    test_restriction_creation(confUrl, auth, page_id, test_user)

def test_standard_api_restrictions(confUrl, auth, page_id, username):
    """Test using standard Confluence API to set restrictions"""
    try:
        # Try to add user restriction using standard API
        url = f"{confUrl}/rest/api/content/{page_id}/restriction"
        
        restriction_data = {
            "operation": "read",
            "restrictions": {
                "user": [
                    {
                        "type": "known",
                        "username": username
                    }
                ]
            }
        }
        
        headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        }
        
        # Try POST (add restriction)
        response = requests.post(url, 
                               json=restriction_data, 
                               auth=auth, 
                               headers=headers,
                               timeout=30)
        
        print(f"   📤 POST restriction: {response.status_code}")
        if response.text:
            print(f"      Response: {response.text[:200]}")
        
        # Try PUT (update restriction)
        response = requests.put(url, 
                              json=restriction_data, 
                              auth=auth, 
                              headers=headers,
                              timeout=30)
        
        print(f"   📤 PUT restriction: {response.status_code}")
        if response.text:
            print(f"      Response: {response.text[:200]}")
            
    except Exception as e:
        print(f"   ❌ Error with standard API: {str(e)}")

def test_current_api(page_id, username):
    """Test our current permission API with detailed response"""
    grant_url = f'https://art.iyc.ishafoundation.org/ArApi/conf/add-perm?pid={page_id}&user={username}'
    
    try:
        print(f"   🔗 URL: {grant_url}")
        response = requests.post(grant_url, timeout=30)
        print(f"   📤 Response: {response.status_code}")
        print(f"   📝 Response text: '{response.text}'")
        print(f"   📋 Response headers: {dict(response.headers)}")
        
        # Also test with authentication
        auth = HTTPBasicAuth('script', 'createpage')
        response_auth = requests.post(grant_url, auth=auth, timeout=30)
        print(f"   📤 With auth: {response_auth.status_code}")
        print(f"   📝 Auth response: '{response_auth.text}'")
        
    except Exception as e:
        print(f"   ❌ Error with current API: {str(e)}")

def test_restriction_creation(confUrl, auth, page_id, username):
    """Test if we need to create restrictions structure first"""
    try:
        # First, try to get current restrictions
        url = f"{confUrl}/rest/api/content/{page_id}/restriction"
        response = requests.get(url, auth=auth, timeout=30)
        
        print(f"   📥 Get restrictions: {response.status_code}")
        
        if response.status_code == 200:
            current_restrictions = response.json()
            print(f"   📋 Current restrictions: {json.dumps(current_restrictions, indent=2)}")
            
            # Try to modify existing restrictions
            if 'results' in current_restrictions:
                results = current_restrictions['results']
                
                # Look for read restrictions
                read_restriction = None
                for result in results:
                    if result.get('operation') == 'read':
                        read_restriction = result
                        break
                
                if read_restriction:
                    print(f"   ✅ Found existing read restriction")
                    
                    # Try to add user to existing restriction
                    existing_users = read_restriction.get('restrictions', {}).get('user', {}).get('results', [])
                    
                    # Add our test user
                    new_user = {
                        "type": "known",
                        "username": username
                    }
                    
                    updated_users = existing_users + [new_user]
                    
                    update_data = {
                        "operation": "read",
                        "restrictions": {
                            "user": updated_users
                        }
                    }
                    
                    # Try to update
                    update_response = requests.put(
                        url,
                        json=update_data,
                        auth=auth,
                        headers={'Content-Type': 'application/json'},
                        timeout=30
                    )
                    
                    print(f"   📤 Update restriction: {update_response.status_code}")
                    if update_response.text:
                        print(f"      Response: {update_response.text[:300]}")
                else:
                    print(f"   ⚠️ No existing read restriction found")
        else:
            print(f"   ❌ Could not get restrictions: {response.text}")
            
    except Exception as e:
        print(f"   ❌ Error testing restriction creation: {str(e)}")

def main():
    """Run restriction investigation"""
    print("🔍 RESTRICTION INVESTIGATION SUITE")
    print("🕐 Started at:", datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
    
    investigate_page_restrictions()
    test_different_permission_approaches()
    
    print("\n" + "="*60)
    print("🎯 INVESTIGATION COMPLETE")
    print("="*60)

if __name__ == "__main__":
    main()
