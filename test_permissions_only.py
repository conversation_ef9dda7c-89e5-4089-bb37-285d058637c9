#!/usr/bin/env python3
"""
🧪 TEST PERMISSIONS ONLY
Test that the script only manages page permissions and doesn't touch workflow
"""

import requests
from requests.auth import HTTPBasicAuth
import json
import base64
from datetime import datetime
import sys

# Add current directory to path to import from app.py
sys.path.append('.')

def test_permissions_only():
    """Test that we only manage permissions, not workflow"""
    print("🧪 TESTING PERMISSIONS-ONLY FUNCTIONALITY")
    print("="*60)
    
    # Configuration
    confUrl = 'https://art.iyc.ishafoundation.org'
    confcwUrl = 'https://art.iyc.ishafoundation.org/rest/cw/1/content'
    conf_username = 'script'
    conf_password = 'createpage'
    conf_auth = HTTPBasicAuth(conf_username, conf_password)
    
    page_id = "151139217"
    test_user = "rohit.dutt"
    
    print(f"📄 Testing with page ID: {page_id}")
    print(f"👤 Testing with user: {test_user}")
    
    # Step 1: Get BEFORE state
    print(f"\n📋 BEFORE - Current State:")
    page_users_before = get_page_users(confUrl, conf_auth, page_id)
    workflow_before = get_workflow_assignments(confcwUrl, conf_auth, page_id)
    
    # Step 2: Test our permission functions
    print(f"\n🔧 TESTING PERMISSION FUNCTIONS")
    print("-" * 40)
    
    # Import functions from updated app.py
    from app import grant_access_to_user, remove_access_from_user
    
    # Test grant
    print(f"Testing GRANT for {test_user}...")
    grant_result = grant_access_to_user(page_id, test_user)
    print(f"Grant result: {grant_result}")
    
    # Small delay
    import time
    time.sleep(2)
    
    # Step 3: Check AFTER grant
    print(f"\n📋 AFTER GRANT - State:")
    page_users_after_grant = get_page_users(confUrl, conf_auth, page_id)
    workflow_after_grant = get_workflow_assignments(confcwUrl, conf_auth, page_id)
    
    # Test remove
    print(f"\n🗑️ Testing REMOVE for {test_user}...")
    remove_result = remove_access_from_user(page_id, test_user)
    print(f"Remove result: {remove_result}")
    
    # Small delay
    time.sleep(2)
    
    # Step 4: Check AFTER remove
    print(f"\n📋 AFTER REMOVE - Final State:")
    page_users_after_remove = get_page_users(confUrl, conf_auth, page_id)
    workflow_after_remove = get_workflow_assignments(confcwUrl, conf_auth, page_id)
    
    # Step 5: Analysis
    print(f"\n📊 ANALYSIS:")
    print(f"   📋 Page users BEFORE: {page_users_before}")
    print(f"   📋 Page users AFTER GRANT: {page_users_after_grant}")
    print(f"   📋 Page users AFTER REMOVE: {page_users_after_remove}")
    print(f"   🔄 Workflow BEFORE: {workflow_before}")
    print(f"   🔄 Workflow AFTER GRANT: {workflow_after_grant}")
    print(f"   🔄 Workflow AFTER REMOVE: {workflow_after_remove}")
    
    # Verify permissions changed but workflow didn't
    page_changed = (page_users_before != page_users_after_grant) or (page_users_after_grant != page_users_after_remove)
    workflow_unchanged = (workflow_before == workflow_after_grant == workflow_after_remove)
    
    print(f"\n🎯 VERIFICATION:")
    if page_changed:
        print(f"   ✅ Page permissions CHANGED (as expected)")
    else:
        print(f"   ⚠️ Page permissions did NOT change")
    
    if workflow_unchanged:
        print(f"   ✅ Workflow assignments UNCHANGED (as expected)")
    else:
        print(f"   ❌ Workflow assignments CHANGED (unexpected!)")
    
    if page_changed and workflow_unchanged:
        print(f"   🎉 SUCCESS: Only permissions managed, workflow untouched!")
    else:
        print(f"   ⚠️ REVIEW: Check if behavior is as expected")

def get_page_users(confUrl, auth, page_id):
    """Get users who have page access"""
    try:
        url = f"{confUrl}/rest/api/content/{page_id}?expand=restrictions.read.restrictions.user"
        response = requests.get(url, auth=auth, timeout=30)
        
        if response.status_code == 200:
            page_data = response.json()
            restrictions = page_data.get('restrictions', {}).get('read', {}).get('restrictions', {})
            users_with_access = restrictions.get('user', {}).get('results', [])
            
            usernames = [user.get('username') for user in users_with_access if user.get('username')]
            print(f"   📋 Page access: {usernames}")
            return usernames
        
        return []
        
    except Exception as e:
        print(f"   ❌ Error getting page users: {str(e)}")
        return []

def get_workflow_assignments(confcwUrl, auth, page_id):
    """Get users assigned to workflow stages"""
    try:
        url = f'{confcwUrl}/{page_id}/status?expand=approvals'
        response = requests.get(url, auth=auth, timeout=30)
        
        if response.status_code == 200:
            page_data = response.json()
            
            state = page_data.get('state', {}).get('name', 'Unknown')
            print(f"   🔄 Workflow state: {state}")
            
            assigned_users = []
            approvals = page_data.get('approvals', [])
            
            for approval in approvals:
                stage_name = approval.get('name', 'Unknown')
                approvers = approval.get('approvers', [])
                
                stage_users = []
                for approver in approvers:
                    user = approver.get('user', {})
                    username = user.get('name')
                    if username:
                        stage_users.append(username)
                        assigned_users.append(username)
                
                if stage_users:
                    print(f"      📝 {stage_name}: {stage_users}")
            
            return assigned_users
        
        return []
        
    except Exception as e:
        print(f"   ❌ Error getting workflow assignments: {str(e)}")
        return []

def test_real_scenario():
    """Test with a real JIRA ticket scenario"""
    print(f"\n🧪 TESTING REAL SCENARIO")
    print("="*60)
    
    # Get a real ticket
    jira_url = "https://servicedesk.isha.in"
    decoded_str = base64.b64decode('YXJjaGl2ZXMuc2NyaXB0OkB1dG9tYXRl').decode('utf-8')
    jira_username, jira_password = decoded_str.split(":")
    jira_auth = HTTPBasicAuth(jira_username, jira_password)
    
    ticket = "AT-59895"
    
    try:
        api_url = f"{jira_url}/rest/api/2/search"
        params = {
            'jql': f'key = "{ticket}"',
            'fields': 'assignee'
        }
        
        response = requests.get(api_url, params=params, auth=jira_auth, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            if data['issues']:
                issue = data['issues'][0]
                assignee = issue['fields']['assignee']
                current_assignee = assignee['name'] if assignee else None
                
                print(f"🎫 Ticket {ticket} current assignee: {current_assignee}")
                
                if current_assignee:
                    # Test with real assignee
                    from app import grant_access_to_user
                    result = grant_access_to_user("151139217", current_assignee)
                    print(f"✅ Real scenario test result: {result}")
                else:
                    print("⚠️ Ticket has no assignee")
            else:
                print("❌ Ticket not found")
        else:
            print(f"❌ JIRA API error: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error in real scenario test: {str(e)}")

def main():
    """Run permissions-only tests"""
    print("🧪 PERMISSIONS-ONLY TESTING SUITE")
    print("🕐 Started at:", datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
    
    # Test 1: Permissions-only functionality
    test_permissions_only()
    
    # Test 2: Real scenario
    test_real_scenario()
    
    print("\n" + "="*60)
    print("🎯 PERMISSIONS-ONLY TESTING COMPLETE")
    print("="*60)
    
    print("\n🎉 EXPECTED BEHAVIOR:")
    print("✅ Page permissions: MANAGED (users appear in restrictions)")
    print("✅ Workflow assignments: UNTOUCHED (no changes to workflow)")
    print("🎯 SCRIPT NOW DOES EXACTLY WHAT YOU REQUESTED!")

if __name__ == "__main__":
    main()
