#!/usr/bin/env python3
"""
🔍 DEEP API INVESTIGATION
Investigate the actual difference between permission APIs and workflow assignment
"""

import requests
from requests.auth import HTTP<PERSON>asic<PERSON>uth
import json
from datetime import datetime

def test_confluence_apis_deeply():
    """Test all Confluence APIs to understand the difference"""
    print("🔍 DEEP CONFLUENCE API INVESTIGATION")
    print("="*70)
    
    # Configuration
    confUrl = 'https://art.iyc.ishafoundation.org'
    confcwUrl = 'https://art.iyc.ishafoundation.org/rest/cw/1/content'
    username = 'script'
    password = 'createpage'
    auth = HTTPBasicAuth(username, password)
    
    page_id = "151139217"
    test_user = "rohit.dutt"
    
    print(f"📄 Testing with page ID: {page_id}")
    print(f"👤 Testing with user: {test_user}")
    
    # Test 1: Check current page restrictions
    print(f"\n1️⃣ CHECKING CURRENT PAGE RESTRICTIONS")
    print("-" * 50)
    current_restrictions = get_page_restrictions(confUrl, auth, page_id)
    
    # Test 2: Check current workflow status
    print(f"\n2️⃣ CHECKING CURRENT WORKFLOW STATUS")
    print("-" * 50)
    workflow_status = get_workflow_status(confcwUrl, auth, page_id)
    
    # Test 3: Test permission API (what app.py uses)
    print(f"\n3️⃣ TESTING PERMISSION API (app.py approach)")
    print("-" * 50)
    test_permission_api(page_id, test_user)
    
    # Test 4: Test workflow assignment API (what refer.py uses)
    print(f"\n4️⃣ TESTING WORKFLOW ASSIGNMENT API (refer.py approach)")
    print("-" * 50)
    test_workflow_assignment(confcwUrl, auth, page_id, test_user)
    
    # Test 5: Check results after both APIs
    print(f"\n5️⃣ CHECKING RESULTS AFTER API CALLS")
    print("-" * 50)
    print("📋 Page restrictions after API calls:")
    get_page_restrictions(confUrl, auth, page_id)
    print("🔄 Workflow status after API calls:")
    get_workflow_status(confcwUrl, auth, page_id)

def get_page_restrictions(confUrl, auth, page_id):
    """Get current page restrictions"""
    try:
        url = f"{confUrl}/rest/api/content/{page_id}?expand=restrictions.read.restrictions.user"
        response = requests.get(url, auth=auth, timeout=30)
        
        if response.status_code == 200:
            page_data = response.json()
            restrictions = page_data.get('restrictions', {}).get('read', {}).get('restrictions', {})
            users_with_access = restrictions.get('user', {}).get('results', [])
            
            print(f"   📋 Users with page access: {len(users_with_access)}")
            for user in users_with_access:
                print(f"      👤 {user.get('username')} ({user.get('displayName')})")
            
            return users_with_access
        else:
            print(f"   ❌ Could not get restrictions: {response.status_code}")
            return []
            
    except Exception as e:
        print(f"   ❌ Error getting restrictions: {str(e)}")
        return []

def get_workflow_status(confcwUrl, auth, page_id):
    """Get current workflow status and assignments"""
    try:
        url = f'{confcwUrl}/{page_id}/status?expand=approvals'
        response = requests.get(url, auth=auth, timeout=30)
        
        if response.status_code == 200:
            page_data = response.json()
            
            print(f"   🔄 Workflow state: {page_data.get('state', {}).get('name', 'Unknown')}")
            
            approvals = page_data.get('approvals', [])
            print(f"   📋 Workflow approvals: {len(approvals)}")
            
            for i, approval in enumerate(approvals):
                print(f"      📝 Approval {i+1}: {approval.get('name', 'Unknown')}")
                approvers = approval.get('approvers', [])
                for approver in approvers:
                    user = approver.get('user', {})
                    print(f"         👤 {user.get('name', 'Unknown')} ({user.get('fullName', 'Unknown')})")
            
            return page_data
        else:
            print(f"   ❌ Could not get workflow status: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"   ❌ Error getting workflow status: {str(e)}")
        return None

def test_permission_api(page_id, username):
    """Test the permission API (what app.py uses)"""
    
    # Test GRANT permission API
    grant_url = f'https://art.iyc.ishafoundation.org/ArApi/conf/add-perm?pid={page_id}&user={username}'
    
    try:
        print(f"   🔧 Testing GRANT: {grant_url}")
        response = requests.post(grant_url, timeout=30)
        print(f"   📤 GRANT Response: {response.status_code}")
        if response.text:
            print(f"      Response text: {response.text}")
        
        # Also test with authentication
        auth = HTTPBasicAuth('script', 'createpage')
        response_auth = requests.post(grant_url, auth=auth, timeout=30)
        print(f"   📤 GRANT with auth: {response_auth.status_code}")
        if response_auth.text:
            print(f"      Response text: {response_auth.text}")
            
    except Exception as e:
        print(f"   ❌ Error testing permission API: {str(e)}")

def test_workflow_assignment(confcwUrl, auth, page_id, username):
    """Test workflow assignment API (what refer.py uses)"""
    
    try:
        # First get current workflow state
        status_url = f'{confcwUrl}/{page_id}/status?expand=approvals'
        response = requests.get(status_url, auth=auth, timeout=30)
        
        if response.status_code == 200:
            page_data = response.json()
            current_state = page_data.get('state', {}).get('name', '')
            
            print(f"   🔄 Current workflow state: {current_state}")
            
            # Map states to assignment stages (from refer.py)
            stage_mapping = {
                'Transcription': 'Transcription',
                'Transcribed': 'I Proofing', 
                'I Proofed': 'II Proofing',
                'II Proofed': 'Proof-Reading'
            }
            
            if current_state in stage_mapping:
                target_stage = stage_mapping[current_state]
                print(f"   🎯 Target assignment stage: {target_stage}")
                
                # Try workflow assignment
                assign_url = f'{confcwUrl}/{page_id}/approvals/assign?expand=state,states,actions,approvals&admin=true'
                
                assignee_data = {
                    'name': target_stage,
                    'assignees': [{'username': username}]
                }
                
                print(f"   🔧 Testing workflow assignment: {assign_url}")
                print(f"   📝 Assignment data: {json.dumps(assignee_data, indent=2)}")
                
                assign_response = requests.patch(
                    assign_url,
                    data=json.dumps(assignee_data),
                    auth=auth,
                    headers={'Content-Type': 'application/json'},
                    timeout=30
                )
                
                print(f"   📤 Assignment Response: {assign_response.status_code}")
                if assign_response.text:
                    print(f"      Response text: {assign_response.text[:500]}")
                
                if assign_response.status_code == 200:
                    print(f"   ✅ Workflow assignment SUCCESS!")
                else:
                    print(f"   ❌ Workflow assignment FAILED")
            else:
                print(f"   ⚠️ Unknown workflow state: {current_state}")
        else:
            print(f"   ❌ Could not get workflow status: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Error testing workflow assignment: {str(e)}")

def test_different_permission_endpoints():
    """Test different permission endpoint variations"""
    print(f"\n6️⃣ TESTING DIFFERENT PERMISSION ENDPOINTS")
    print("-" * 50)
    
    page_id = "151139217"
    username = "rohit.dutt"
    auth = HTTPBasicAuth('script', 'createpage')
    
    # Test different endpoint variations
    endpoints = [
        f'https://art.iyc.ishafoundation.org/ArApi/conf/add-perm?pid={page_id}&user={username}',
        f'https://gnanetra.iyc.ishafoundation.org/ArApi/conf/add-perm?pid={page_id}&user={username}',
        f'https://art.iyc.ishafoundation.org/ArApi/add-perm?pid={page_id}&user={username}',
        f'https://gnanetra.iyc.ishafoundation.org/ArApi/add-perm?pid={page_id}&user={username}',
    ]
    
    for endpoint in endpoints:
        try:
            print(f"   🔧 Testing: {endpoint}")
            
            # Test without auth
            response = requests.post(endpoint, timeout=10)
            print(f"      📤 No auth: {response.status_code}")
            
            # Test with auth
            response_auth = requests.post(endpoint, auth=auth, timeout=10)
            print(f"      📤 With auth: {response_auth.status_code}")
            
            if response_auth.text and len(response_auth.text) < 100:
                print(f"         Response: {response_auth.text}")
                
        except Exception as e:
            print(f"      ❌ Error: {str(e)}")

def main():
    """Run deep API investigation"""
    print("🔍 DEEP API INVESTIGATION SUITE")
    print("🕐 Started at:", datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
    
    test_confluence_apis_deeply()
    test_different_permission_endpoints()
    
    print("\n" + "="*70)
    print("🎯 DEEP INVESTIGATION COMPLETE")
    print("="*70)
    
    print("\n📊 KEY FINDINGS:")
    print("🔍 Permission API vs Workflow Assignment:")
    print("   📋 Permission API: Grants page view/edit access")
    print("   🔄 Workflow API: Assigns users to workflow stages")
    print("   💡 These are DIFFERENT operations!")
    
    print("\n🎯 RECOMMENDATION:")
    print("   Use workflow assignment API for actual user assignment")
    print("   Use permission API only for access control")

if __name__ == "__main__":
    main()
