/**
 * 🔧 CONFLUENCE SCRIPTRUNNER SOLUTION
 * Groovy script to enable page-level permissions and create REST endpoints
 * This will solve all the permission API issues
 */

import com.atlassian.confluence.api.model.content.Content
import com.atlassian.confluence.api.model.content.ContentId
import com.atlassian.confluence.api.service.content.ContentService
import com.atlassian.confluence.security.ContentPermission
import com.atlassian.confluence.security.************************
import com.atlassian.confluence.security.SpacePermission
import com.atlassian.confluence.security.SpacePermissionManager
import com.atlassian.confluence.spaces.Space
import com.atlassian.confluence.spaces.SpaceManager
import com.atlassian.confluence.user.UserAccessor
import com.atlassian.user.User
import com.atlassian.sal.api.component.ComponentLocator
import com.atlassian.webresource.api.assembler.PageBuilderService
import groovy.json.JsonBuilder
import groovy.json.JsonSlurper

import javax.servlet.http.HttpServletRequest
import javax.servlet.http.HttpServletResponse

// Get required services
def contentService = ComponentLocator.getComponent(ContentService)
def contentPermissionManager = ComponentLocator.getComponent(************************)
def spacePermissionManager = ComponentLocator.getComponent(SpacePermissionManager)
def spaceManager = ComponentLocator.getComponent(SpaceManager)
def userAccessor = ComponentLocator.getComponent(UserAccessor)

/**
 * 1️⃣ ENABLE SPACE-LEVEL PERMISSIONS
 * Configure the "Temp" space to allow individual page restrictions
 */
def enableSpacePermissions() {
    log.info("🔧 Enabling space-level permissions for Temp space...")
    
    try {
        // Get the Temp space
        Space tempSpace = spaceManager.getSpace("TEMP")
        if (!tempSpace) {
            log.error("❌ Temp space not found")
            return false
        }
        
        log.info("📚 Found space: ${tempSpace.getName()} (${tempSpace.getKey()})")
        
        // Check current space permissions
        def currentPermissions = spacePermissionManager.getSpacePermissions(tempSpace)
        log.info("🔒 Current space permissions: ${currentPermissions.size()}")
        
        // Add basic space permissions if missing
        def requiredPermissions = [
            SpacePermission.VIEWSPACE_PERMISSION,
            SpacePermission.EDITSPACE_PERMISSION,
            SpacePermission.EDITBLOG_PERMISSION
        ]
        
        // Get script user (or admin user)
        User scriptUser = userAccessor.getUserByName("script")
        if (!scriptUser) {
            scriptUser = userAccessor.getUserByName("admin")
        }
        
        if (scriptUser) {
            for (String permission : requiredPermissions) {
                if (!spacePermissionManager.hasPermission(permission, tempSpace, scriptUser)) {
                    SpacePermission spacePermission = SpacePermission.createUserPermission(permission, scriptUser)
                    spacePermissionManager.savePermission(spacePermission, tempSpace)
                    log.info("✅ Added ${permission} for user ${scriptUser.getName()}")
                }
            }
        }
        
        log.info("✅ Space permissions configured successfully")
        return true
        
    } catch (Exception e) {
        log.error("❌ Error enabling space permissions: ${e.message}")
        return false
    }
}

/**
 * 2️⃣ GRANT PAGE PERMISSION FUNCTION
 * Internal function to grant page-level permissions
 */
def grantPagePermission(String pageId, String username) {
    log.info("🔧 Granting page permission: ${pageId} -> ${username}")
    
    try {
        // Get the page
        ContentId contentId = ContentId.of(Long.parseLong(pageId))
        Content page = contentService.find(contentId).orElse(null)
        
        if (!page) {
            log.error("❌ Page not found: ${pageId}")
            return [success: false, message: "Page not found"]
        }
        
        // Get the user
        User user = userAccessor.getUserByName(username)
        if (!user) {
            log.error("❌ User not found: ${username}")
            return [success: false, message: "User not found"]
        }
        
        // Check if user already has permission
        if (contentPermissionManager.hasContentLevelPermission(user, ContentPermission.VIEW_PERMISSION, page)) {
            log.info("ℹ️ User ${username} already has permission for page ${pageId}")
            return [success: true, message: "User already has permission"]
        }
        
        // Create and save VIEW permission
        ContentPermission viewPermission = ContentPermission.createUserPermission(
            ContentPermission.VIEW_PERMISSION, user, page
        )
        contentPermissionManager.addContentPermission(viewPermission)
        
        // Create and save EDIT permission
        ContentPermission editPermission = ContentPermission.createUserPermission(
            ContentPermission.EDIT_PERMISSION, user, page
        )
        contentPermissionManager.addContentPermission(editPermission)
        
        log.info("✅ Successfully granted permissions to ${username} for page ${pageId}")
        return [success: true, message: "Permission granted successfully"]
        
    } catch (Exception e) {
        log.error("❌ Error granting page permission: ${e.message}")
        return [success: false, message: "Error: ${e.message}"]
    }
}

/**
 * 3️⃣ REMOVE PAGE PERMISSION FUNCTION
 * Internal function to remove page-level permissions
 */
def removePagePermission(String pageId, String username) {
    log.info("🗑️ Removing page permission: ${pageId} -> ${username}")
    
    try {
        // Get the page
        ContentId contentId = ContentId.of(Long.parseLong(pageId))
        Content page = contentService.find(contentId).orElse(null)
        
        if (!page) {
            log.error("❌ Page not found: ${pageId}")
            return [success: false, message: "Page not found"]
        }
        
        // Get the user
        User user = userAccessor.getUserByName(username)
        if (!user) {
            log.error("❌ User not found: ${username}")
            return [success: false, message: "User not found"]
        }
        
        // Get existing permissions for this page and user
        def permissions = contentPermissionManager.getContentPermissions(page)
        def userPermissions = permissions.findAll { 
            it.getUserSubject()?.getName() == username 
        }
        
        if (userPermissions.isEmpty()) {
            log.info("ℹ️ User ${username} has no permissions for page ${pageId}")
            return [success: true, message: "User had no permissions"]
        }
        
        // Remove all permissions for this user
        userPermissions.each { permission ->
            contentPermissionManager.removeContentPermission(permission)
            log.info("🗑️ Removed ${permission.getType()} permission for ${username}")
        }
        
        log.info("✅ Successfully removed permissions from ${username} for page ${pageId}")
        return [success: true, message: "Permissions removed successfully"]
        
    } catch (Exception e) {
        log.error("❌ Error removing page permission: ${e.message}")
        return [success: false, message: "Error: ${e.message}"]
    }
}

/**
 * 4️⃣ CREATE REST ENDPOINT FOR GRANTING PERMISSIONS
 * This creates the missing /ArApi/conf/add-perm endpoint
 */
def createGrantPermissionEndpoint() {
    log.info("🔧 Creating grant permission REST endpoint...")
    
    // This would typically be done through ScriptRunner's REST endpoint feature
    // or by creating a custom servlet
    
    return """
    // REST Endpoint Configuration for ScriptRunner
    // Path: /rest/scriptrunner/latest/custom/grantPermission
    // Method: POST
    // Parameters: pid (page ID), user (username)
    
    import com.onresolve.scriptrunner.runner.rest.common.CustomEndpointDelegate
    import groovy.json.JsonBuilder
    import groovy.transform.BaseScript
    
    @BaseScript CustomEndpointDelegate delegate
    
    grantPermission(httpMethod: "POST") { MultivaluedMap queryParams, String body ->
        def pageId = queryParams.getFirst("pid") as String
        def username = queryParams.getFirst("user") as String
        
        if (!pageId || !username) {
            return Response.status(400).entity([error: "Missing pid or user parameter"]).build()
        }
        
        def result = grantPagePermission(pageId, username)
        
        if (result.success) {
            return Response.ok([status: "success", message: result.message]).build()
        } else {
            return Response.status(500).entity([error: result.message]).build()
        }
    }
    """
}

/**
 * 5️⃣ TEST FUNCTION
 * Test the permission functions with real data
 */
def testPermissionFunctions() {
    log.info("🧪 Testing permission functions...")
    
    def testPageId = "151139217"
    def testUsername = "rohit.dutt"
    
    // Test grant permission
    log.info("Testing grant permission...")
    def grantResult = grantPagePermission(testPageId, testUsername)
    log.info("Grant result: ${grantResult}")
    
    // Wait a moment
    Thread.sleep(1000)
    
    // Test remove permission
    log.info("Testing remove permission...")
    def removeResult = removePagePermission(testPageId, testUsername)
    log.info("Remove result: ${removeResult}")
    
    return [grant: grantResult, remove: removeResult]
}

/**
 * 6️⃣ MAIN EXECUTION
 * Run the setup and tests
 */
def main() {
    log.info("🚀 Starting Confluence Permission Setup...")
    
    // Step 1: Enable space permissions
    def spaceResult = enableSpacePermissions()
    log.info("Space setup result: ${spaceResult}")
    
    // Step 2: Test permission functions
    def testResults = testPermissionFunctions()
    log.info("Test results: ${testResults}")
    
    // Step 3: Show endpoint creation info
    def endpointInfo = createGrantPermissionEndpoint()
    log.info("Endpoint creation info: ${endpointInfo}")
    
    // Summary
    log.info("=" * 60)
    log.info("🎯 SETUP COMPLETE")
    log.info("=" * 60)
    log.info("✅ Space permissions: ${spaceResult ? 'ENABLED' : 'FAILED'}")
    log.info("✅ Grant function: ${testResults.grant.success ? 'WORKING' : 'FAILED'}")
    log.info("✅ Remove function: ${testResults.remove.success ? 'WORKING' : 'FAILED'}")
    log.info("📝 Next: Create REST endpoints using ScriptRunner REST feature")
    
    return [
        spacePermissions: spaceResult,
        grantFunction: testResults.grant.success,
        removeFunction: testResults.remove.success,
        endpointInfo: endpointInfo
    ]
}

// Execute the main function
return main()
