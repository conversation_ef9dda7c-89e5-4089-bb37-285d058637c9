#!/usr/bin/env python3
"""
🔧 REAL PERMISSION API TESTING
Tests the actual permission APIs with proper authentication and methods
"""

import requests
from requests.auth import HTTPBasicAuth
import json
from datetime import datetime

def test_permission_api_with_auth():
    """Test permission APIs with proper authentication"""
    print("🔧 TESTING PERMISSION APIs WITH AUTHENTICATION")
    print("="*60)
    
    # Use Confluence credentials for the API calls
    username = 'script'
    password = 'createpage'
    auth = HTTPBasicAuth(username, password)
    
    page_id = "151139217"
    test_user = "ancy.ravindra"
    
    # Test different base URLs and methods
    api_configs = [
        {
            "base": "https://art.iyc.ishafoundation.org/ArApi/conf",
            "add_endpoint": "add-perm",
            "remove_endpoint": "rm-perm"
        },
        {
            "base": "https://gnanetra.iyc.ishafoundation.org/ArApi/conf", 
            "add_endpoint": "add-perm",
            "remove_endpoint": "rm-perm"
        }
    ]
    
    for config in api_configs:
        print(f"\n🌐 Testing API Base: {config['base']}")
        
        # Test ADD permission
        add_url = f"{config['base']}/{config['add_endpoint']}?pid={page_id}&user={test_user}"
        print(f"   🔗 ADD URL: {add_url}")
        
        try:
            # Try POST method
            response = requests.post(add_url, auth=auth, timeout=30)
            print(f"   📤 POST Response: {response.status_code}")
            if response.text:
                print(f"   📝 Response Text: {response.text[:200]}")
            
            # Try GET method if POST fails
            if response.status_code != 200:
                response = requests.get(add_url, auth=auth, timeout=30)
                print(f"   📥 GET Response: {response.status_code}")
                if response.text:
                    print(f"   📝 Response Text: {response.text[:200]}")
                    
        except Exception as e:
            print(f"   ❌ Error: {str(e)}")
        
        # Test REMOVE permission
        remove_url = f"{config['base']}/{config['remove_endpoint']}?pid={page_id}&user={test_user}"
        print(f"   🔗 REMOVE URL: {remove_url}")
        
        try:
            # Try POST method
            response = requests.post(remove_url, auth=auth, timeout=30)
            print(f"   📤 POST Response: {response.status_code}")
            if response.text:
                print(f"   📝 Response Text: {response.text[:200]}")
                
        except Exception as e:
            print(f"   ❌ Error: {str(e)}")

def test_confluence_standard_api():
    """Test using standard Confluence REST API for permissions"""
    print("\n🔧 TESTING STANDARD CONFLUENCE API")
    print("="*60)
    
    confUrl = 'https://art.iyc.ishafoundation.org'
    username = 'script'
    password = 'createpage'
    auth = HTTPBasicAuth(username, password)
    
    page_id = "151139217"
    test_user = "ancy.ravindra"
    
    # Get current page restrictions
    try:
        url = f"{confUrl}/rest/api/content/{page_id}/restriction"
        response = requests.get(url, auth=auth, timeout=30)
        
        print(f"📋 Get Restrictions Status: {response.status_code}")
        if response.status_code == 200:
            restrictions = response.json()
            print(f"✅ Current restrictions retrieved")
            print(f"📝 Restrictions: {json.dumps(restrictions, indent=2)}")
        else:
            print(f"❌ Cannot get restrictions: {response.text}")
            
    except Exception as e:
        print(f"❌ Error getting restrictions: {str(e)}")
    
    # Try to add user restriction using standard API
    try:
        url = f"{confUrl}/rest/api/content/{page_id}/restriction"
        
        # Standard Confluence restriction format
        restriction_data = {
            "operation": "read",
            "restrictions": {
                "user": [
                    {
                        "type": "known",
                        "username": test_user
                    }
                ]
            }
        }
        
        headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        }
        
        response = requests.post(url, 
                               json=restriction_data, 
                               auth=auth, 
                               headers=headers,
                               timeout=30)
        
        print(f"📤 Add Restriction Status: {response.status_code}")
        if response.status_code in [200, 201]:
            print(f"✅ Successfully added restriction via standard API")
            result = response.json()
            print(f"📝 Result: {json.dumps(result, indent=2)}")
        else:
            print(f"❌ Failed to add restriction: {response.text}")
            
    except Exception as e:
        print(f"❌ Error adding restriction: {str(e)}")

def test_with_session_and_cookies():
    """Test APIs with session management and cookies"""
    print("\n🔧 TESTING WITH SESSION MANAGEMENT")
    print("="*60)
    
    session = requests.Session()
    username = 'script'
    password = 'createpage'
    auth = HTTPBasicAuth(username, password)
    
    # First, authenticate with Confluence to get session
    confUrl = 'https://art.iyc.ishafoundation.org'
    
    try:
        # Get a page to establish session
        response = session.get(f"{confUrl}/rest/api/content/151139217", auth=auth, timeout=30)
        print(f"🔐 Session establishment: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Session established with Confluence")
            
            # Now try the permission API with the established session
            page_id = "151139217"
            test_user = "ancy.ravindra"
            
            # Try the art.iyc.ishafoundation.org API with session
            api_url = f"https://art.iyc.ishafoundation.org/ArApi/conf/add-perm?pid={page_id}&user={test_user}"
            
            # Try different methods
            for method in ['POST', 'GET', 'PUT']:
                try:
                    if method == 'POST':
                        response = session.post(api_url, auth=auth, timeout=30)
                    elif method == 'GET':
                        response = session.get(api_url, auth=auth, timeout=30)
                    elif method == 'PUT':
                        response = session.put(api_url, auth=auth, timeout=30)
                    
                    print(f"   {method} Response: {response.status_code}")
                    if response.text and len(response.text) < 500:
                        print(f"   Response: {response.text}")
                    
                    if response.status_code == 200:
                        print(f"   ✅ SUCCESS with {method} method!")
                        break
                        
                except Exception as e:
                    print(f"   ❌ {method} Error: {str(e)}")
        
    except Exception as e:
        print(f"❌ Session error: {str(e)}")

def test_direct_page_access():
    """Test if we can directly access and modify page permissions"""
    print("\n🔧 TESTING DIRECT PAGE ACCESS")
    print("="*60)
    
    confUrl = 'https://art.iyc.ishafoundation.org'
    username = 'script'
    password = 'createpage'
    auth = HTTPBasicAuth(username, password)
    
    page_id = "151139217"
    
    # Get page details
    try:
        url = f"{confUrl}/rest/api/content/{page_id}?expand=space,restrictions.read.restrictions.user"
        response = requests.get(url, auth=auth, timeout=30)
        
        print(f"📄 Page Details Status: {response.status_code}")
        if response.status_code == 200:
            page_data = response.json()
            print(f"✅ Page Title: {page_data.get('title', 'Unknown')}")
            print(f"📚 Space: {page_data.get('space', {}).get('name', 'Unknown')}")
            
            # Check current restrictions
            restrictions = page_data.get('restrictions', {})
            print(f"🔒 Current Restrictions: {json.dumps(restrictions, indent=2)}")
            
        else:
            print(f"❌ Cannot get page details: {response.text}")
            
    except Exception as e:
        print(f"❌ Error getting page details: {str(e)}")

def main():
    """Run all permission API tests"""
    print("🔧 REAL PERMISSION API TESTING")
    print("🕐 Started at:", datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
    
    test_permission_api_with_auth()
    test_confluence_standard_api()
    test_with_session_and_cookies()
    test_direct_page_access()
    
    print("\n" + "="*60)
    print("🎯 PERMISSION API TESTING COMPLETE")
    print("="*60)
    print("🕐 Completed at:", datetime.now().strftime('%Y-%m-%d %H:%M:%S'))

if __name__ == "__main__":
    main()
