#!/usr/bin/env python3
"""
🧪 REAL WORKFLOW TESTING
Test the complete workflow with actual JIRA tickets and permission APIs
"""

import requests
from requests.auth import HTTPBasicAuth
import json
import base64
import urllib.parse
from datetime import datetime
import os

# Import functions from app.py
import sys
sys.path.append('.')

def test_complete_workflow():
    """Test the complete workflow with real tickets"""
    print("🧪 TESTING COMPLETE WORKFLOW WITH REAL TICKETS")
    print("="*70)
    
    # Configuration (same as app.py)
    jira_url = "https://servicedesk.isha.in"
    decoded_str = base64.b64decode('YXJjaGl2ZXMuc2NyaXB0OkB1dG9tYXRl').decode('utf-8')
    jira_username, jira_password = decoded_str.split(":")
    jira_auth = HTTPBasicAuth(jira_username, jira_password)
    
    confUrl = 'https://art.iyc.ishafoundation.org'
    conf_username = 'script'
    conf_password = 'createpage'
    conf_auth = HTTPBasicAuth(conf_username, conf_password)
    
    # Test with real tickets
    test_tickets = ["AT-59896", "AT-59895", "AT-59898", "AT-59899"]
    
    for ticket in test_tickets:
        print(f"\n🎯 TESTING WORKFLOW FOR {ticket}")
        print("-" * 50)
        
        # Step 1: Get ticket data
        try:
            api_url = f"{jira_url}/rest/api/2/search"
            params = {
                'jql': f'key = "{ticket}"',
                'fields': 'key,summary,assignee,customfield_111118'
            }
            
            response = requests.get(api_url, params=params, auth=jira_auth, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                if data['issues']:
                    issue = data['issues'][0]
                    current_assignee = issue['fields']['assignee']['name'] if issue['fields']['assignee'] else ''
                    confluence_link = issue['fields'].get('customfield_111118')
                    
                    print(f"✅ Step 1: Retrieved {ticket}")
                    print(f"   👤 Current Assignee: {current_assignee}")
                    print(f"   🔗 Confluence Link: {confluence_link}")
                    
                    # Step 2: Get page ID
                    if confluence_link:
                        title = confluence_link.split('/')[-1].strip()
                        encoded_title = urllib.parse.quote(title)
                        page_url = f'{confUrl}/rest/api/content/?title={encoded_title}'
                        
                        page_response = requests.get(page_url, auth=conf_auth, timeout=30)
                        if page_response.status_code == 200:
                            page_data = page_response.json()
                            if page_data['results']:
                                page_id = page_data['results'][0]['id']
                                print(f"✅ Step 2: Found page ID {page_id}")
                                
                                # Step 3: Test permission operations
                                test_permission_operations(page_id, current_assignee)
                            else:
                                print(f"❌ Step 2: Page not found")
                        else:
                            print(f"❌ Step 2: Page lookup failed ({page_response.status_code})")
                    else:
                        print(f"⚠️ Step 2: No Confluence link")
                else:
                    print(f"❌ Step 1: {ticket} not found")
            else:
                print(f"❌ Step 1: JIRA API error ({response.status_code})")
                
        except Exception as e:
            print(f"❌ Error testing {ticket}: {str(e)}")

def test_permission_operations(page_id, username):
    """Test actual permission operations"""
    print(f"🔧 Step 3: Testing permission operations for {username}")
    
    if not username:
        print("   ⚠️ No username to test")
        return
    
    # Test 1: Check current access
    current_access = check_user_has_access(page_id, username)
    print(f"   📋 Current access check: {current_access}")
    
    # Test 2: Try to grant access
    print(f"   🔧 Testing GRANT access...")
    grant_result = test_grant_access(page_id, username)
    print(f"   📤 Grant result: {grant_result}")
    
    # Test 3: Try to remove access (using working API)
    print(f"   🗑️ Testing REMOVE access...")
    remove_result = test_remove_access(page_id, username)
    print(f"   📥 Remove result: {remove_result}")

def check_user_has_access(page_id, username):
    """Check if user has access (same as app.py)"""
    confUrl = 'https://art.iyc.ishafoundation.org'
    conf_username = 'script'
    conf_password = 'createpage'
    auth = HTTPBasicAuth(conf_username, conf_password)
    
    try:
        url = f"{confUrl}/rest/api/content/{page_id}?expand=restrictions.read.restrictions.user"
        response = requests.get(url, auth=auth, timeout=30)
        
        if response.status_code == 200:
            page_data = response.json()
            restrictions = page_data.get('restrictions', {}).get('read', {}).get('restrictions', {})
            users_with_access = restrictions.get('user', {}).get('results', [])
            
            print(f"   📋 Users with access: {[u.get('username') for u in users_with_access]}")
            
            for user in users_with_access:
                if user.get('username') == username:
                    return True
        
        return False
        
    except Exception as e:
        print(f'   ⚠️ Could not check access: {str(e)}')
        return False

def test_grant_access(page_id, username):
    """Test granting access with multiple strategies"""
    
    # Strategy 1: Try different API endpoints
    api_endpoints = [
        f'https://gnanetra.iyc.ishafoundation.org/ArApi/conf/add-perm?pid={page_id}&user={username}',
        f'https://gnanetra.iyc.ishafoundation.org/ArApi/add-perm?pid={page_id}&user={username}',
        f'https://art.iyc.ishafoundation.org/ArApi/conf/add-perm?pid={page_id}&user={username}',
    ]
    
    for url in api_endpoints:
        try:
            response = requests.post(url, timeout=30)
            print(f"      🌐 {url}: {response.status_code}")
            if response.text and len(response.text) < 100:
                print(f"         Response: {response.text}")
            
            if response.status_code == 200:
                return "SUCCESS"
                
        except Exception as e:
            print(f"      ❌ Error: {str(e)}")
    
    return "FAILED_API_NOT_AVAILABLE"

def test_remove_access(page_id, username):
    """Test removing access (we know this works)"""
    url = f'https://gnanetra.iyc.ishafoundation.org/ArApi/conf/rm-perm?pid={page_id}&user={username}'
    
    try:
        response = requests.post(url, timeout=30)
        print(f"      🌐 {url}: {response.status_code}")
        if response.text:
            print(f"         Response: {response.text}")
        
        if response.status_code == 200:
            return "SUCCESS"
        else:
            return f"FAILED_STATUS_{response.status_code}"
            
    except Exception as e:
        print(f"      ❌ Error: {str(e)}")
        return "FAILED_EXCEPTION"

def test_manual_logging():
    """Test the manual logging functionality"""
    print("\n🧪 TESTING MANUAL LOGGING FUNCTIONALITY")
    print("="*50)
    
    # Test logging a manual request
    test_page_id = "151139217"
    test_user = "test.user"
    
    log_manual_permission_request(test_page_id, test_user, 'GRANT')
    log_manual_permission_request(test_page_id, test_user, 'REMOVE')
    
    # Check if log file was created
    if os.path.exists('manual_permission_requests.json'):
        with open('manual_permission_requests.json', 'r') as f:
            requests_log = json.load(f)
        print(f"✅ Manual log created with {len(requests_log)} entries")
        for entry in requests_log:
            print(f"   📝 {entry['timestamp']}: {entry['action']} {entry['username']} on page {entry['page_id']}")
    else:
        print("❌ Manual log file not created")

def log_manual_permission_request(page_id, username, action):
    """Log permission requests that need manual intervention (same as app.py)"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    log_entry = {
        'timestamp': timestamp,
        'page_id': page_id,
        'username': username,
        'action': action,
        'status': 'PENDING_MANUAL_REVIEW'
    }
    
    try:
        manual_log_file = 'manual_permission_requests.json'
        manual_requests = []
        
        if os.path.exists(manual_log_file):
            with open(manual_log_file, 'r', encoding='utf-8') as f:
                manual_requests = json.load(f)
        
        manual_requests.append(log_entry)
        
        with open(manual_log_file, 'w', encoding='utf-8') as f:
            json.dump(manual_requests, f, indent=2)
        
        print(f'   📝 Logged manual request: {action} access for "{username}"')
        
    except Exception as e:
        print(f'   ⚠️ Could not log manual request: {str(e)}')

def main():
    """Run complete workflow testing"""
    print("🧪 REAL WORKFLOW TESTING SUITE")
    print("🕐 Started at:", datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
    
    # Test 1: Complete workflow
    test_complete_workflow()
    
    # Test 2: Manual logging
    test_manual_logging()
    
    print("\n" + "="*70)
    print("🎯 REAL WORKFLOW TESTING COMPLETE")
    print("="*70)
    print("🕐 Completed at:", datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
    
    # Summary
    print("\n📊 SUMMARY:")
    print("✅ JIRA ticket retrieval: WORKING")
    print("✅ Confluence page lookup: WORKING") 
    print("✅ Permission removal API: WORKING")
    print("❌ Permission grant API: NOT AVAILABLE")
    print("✅ Manual logging fallback: WORKING")
    print("✅ User access checking: WORKING")

if __name__ == "__main__":
    main()
