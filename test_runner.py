#!/usr/bin/env python3
"""
🧪 JIRA-Confluence Sync Test Runner
Comprehensive testing script for the permission sync system
"""

import subprocess
import sys
import os
from datetime import datetime

def print_header(title):
    """Print a formatted header"""
    print("\n" + "=" * 80)
    print(f"🧪 {title}")
    print("=" * 80)

def run_test(test_type, description):
    """Run a specific test and capture output"""
    print(f"\n🔍 {description}")
    print("-" * 60)
    
    try:
        if test_type == "full":
            result = subprocess.run([sys.executable, "app.py", "test"], 
                                  capture_output=True, text=True, timeout=120)
        elif test_type == "tickets":
            result = subprocess.run([sys.executable, "app.py", "test-tickets"], 
                                  capture_output=True, text=True, timeout=60)
        elif test_type == "normal":
            result = subprocess.run([sys.executable, "app.py"], 
                                  capture_output=True, text=True, timeout=180)
        else:
            print("❌ Unknown test type")
            return False
        
        print("📤 STDOUT:")
        print(result.stdout)
        
        if result.stderr:
            print("📥 STDERR:")
            print(result.stderr)
        
        if result.returncode == 0:
            print("✅ Test completed successfully")
            return True
        else:
            print(f"❌ Test failed with return code: {result.returncode}")
            return False
            
    except subprocess.TimeoutExpired:
        print("⏰ Test timed out")
        return False
    except Exception as e:
        print(f"❌ Test error: {str(e)}")
        return False

def main():
    """Main test runner"""
    print_header("JIRA-CONFLUENCE SYNC TEST SUITE")
    print(f"🕐 Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    if not os.path.exists("app.py"):
        print("❌ app.py not found in current directory")
        return
    
    tests_passed = 0
    total_tests = 0
    
    # Test 1: Component Testing
    total_tests += 1
    print_header("TEST 1: COMPONENT TESTING")
    if run_test("full", "Testing all system components"):
        tests_passed += 1
    
    # Test 2: Specific Ticket Testing
    total_tests += 1
    print_header("TEST 2: SPECIFIC TICKET TESTING")
    if run_test("tickets", "Testing specific JIRA tickets (AT-59896, AT-59894, AT-59895, AT-59898, AT-59899)"):
        tests_passed += 1
    
    # Test 3: Normal Execution
    total_tests += 1
    print_header("TEST 3: NORMAL EXECUTION")
    if run_test("normal", "Running normal sync process"):
        tests_passed += 1
    
    # Summary
    print_header("TEST SUMMARY")
    print(f"📊 Tests Passed: {tests_passed}/{total_tests}")
    print(f"🕐 Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    if tests_passed == total_tests:
        print("🎉 ALL TESTS PASSED!")
        return 0
    else:
        print("⚠️ SOME TESTS FAILED")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
