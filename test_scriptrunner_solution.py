#!/usr/bin/env python3
"""
🧪 TEST SCRIPTRUNNER SOLUTION
Test the ScriptRunner-based permission endpoints
"""

import requests
from requests.auth import HTTPBasicAuth
import json
from datetime import datetime

def test_scriptrunner_endpoints():
    """Test the ScriptRunner permission endpoints"""
    print("🧪 TESTING SCRIPTRUNNER PERMISSION ENDPOINTS")
    print("="*60)
    
    # Configuration
    base_url = 'https://art.iyc.ishafoundation.org'
    auth = HTTPBasicAuth('script', 'createpage')
    
    page_id = "151139217"
    test_user = "rohit.dutt"
    
    # Test endpoints
    grant_endpoint = f"{base_url}/rest/scriptrunner/latest/custom/grantPermission"
    remove_endpoint = f"{base_url}/rest/scriptrunner/latest/custom/removePermission"
    
    print(f"📄 Testing with page ID: {page_id}")
    print(f"👤 Testing with user: {test_user}")
    
    # Step 1: Check current page restrictions
    print(f"\n1️⃣ CHECKING CURRENT PAGE RESTRICTIONS")
    print("-" * 40)
    current_users = get_page_restrictions(base_url, auth, page_id)
    
    # Step 2: Test grant permission
    print(f"\n2️⃣ TESTING GRANT PERMISSION ENDPOINT")
    print("-" * 40)
    grant_result = test_grant_permission(grant_endpoint, auth, page_id, test_user)
    
    # Step 3: Check restrictions after grant
    print(f"\n3️⃣ CHECKING RESTRICTIONS AFTER GRANT")
    print("-" * 40)
    users_after_grant = get_page_restrictions(base_url, auth, page_id)
    
    # Step 4: Test remove permission
    print(f"\n4️⃣ TESTING REMOVE PERMISSION ENDPOINT")
    print("-" * 40)
    remove_result = test_remove_permission(remove_endpoint, auth, page_id, test_user)
    
    # Step 5: Check restrictions after remove
    print(f"\n5️⃣ CHECKING RESTRICTIONS AFTER REMOVE")
    print("-" * 40)
    users_after_remove = get_page_restrictions(base_url, auth, page_id)
    
    # Step 6: Analysis
    print(f"\n6️⃣ ANALYSIS")
    print("-" * 40)
    print(f"   📋 Users before: {current_users}")
    print(f"   📋 Users after grant: {users_after_grant}")
    print(f"   📋 Users after remove: {users_after_remove}")
    
    # Check if user was added and removed
    user_added = test_user in users_after_grant and test_user not in current_users
    user_removed = test_user not in users_after_remove and test_user in users_after_grant
    
    print(f"\n🎯 RESULTS:")
    print(f"   ✅ Grant endpoint: {'WORKING' if grant_result else 'FAILED'}")
    print(f"   ✅ Remove endpoint: {'WORKING' if remove_result else 'FAILED'}")
    print(f"   ✅ User added to restrictions: {'YES' if user_added else 'NO'}")
    print(f"   ✅ User removed from restrictions: {'YES' if user_removed else 'NO'}")
    
    if grant_result and remove_result and user_added and user_removed:
        print(f"   🎉 SCRIPTRUNNER SOLUTION: FULLY WORKING!")
    else:
        print(f"   ⚠️ SCRIPTRUNNER SOLUTION: NEEDS DEBUGGING")

def get_page_restrictions(base_url, auth, page_id):
    """Get current page restrictions"""
    try:
        url = f"{base_url}/rest/api/content/{page_id}?expand=restrictions.read.restrictions.user"
        response = requests.get(url, auth=auth, timeout=30)
        
        if response.status_code == 200:
            page_data = response.json()
            restrictions = page_data.get('restrictions', {}).get('read', {}).get('restrictions', {})
            users_with_access = restrictions.get('user', {}).get('results', [])
            
            usernames = [user.get('username') for user in users_with_access if user.get('username')]
            print(f"   📋 Current users with access: {usernames}")
            return usernames
        else:
            print(f"   ❌ Could not get restrictions: {response.status_code}")
            return []
        
    except Exception as e:
        print(f"   ❌ Error getting restrictions: {str(e)}")
        return []

def test_grant_permission(endpoint, auth, page_id, username):
    """Test the grant permission endpoint"""
    try:
        url = f"{endpoint}?pid={page_id}&user={username}"
        print(f"   🔗 URL: {url}")
        
        response = requests.post(url, auth=auth, timeout=30)
        print(f"   📤 Response: {response.status_code}")
        
        if response.text:
            try:
                response_data = response.json()
                print(f"   📝 Response: {json.dumps(response_data, indent=2)}")
            except:
                print(f"   📝 Response text: {response.text[:200]}")
        
        return response.status_code == 200
        
    except Exception as e:
        print(f"   ❌ Error testing grant: {str(e)}")
        return False

def test_remove_permission(endpoint, auth, page_id, username):
    """Test the remove permission endpoint"""
    try:
        url = f"{endpoint}?pid={page_id}&user={username}"
        print(f"   🔗 URL: {url}")
        
        response = requests.post(url, auth=auth, timeout=30)
        print(f"   📤 Response: {response.status_code}")
        
        if response.text:
            try:
                response_data = response.json()
                print(f"   📝 Response: {json.dumps(response_data, indent=2)}")
            except:
                print(f"   📝 Response text: {response.text[:200]}")
        
        return response.status_code == 200
        
    except Exception as e:
        print(f"   ❌ Error testing remove: {str(e)}")
        return False

def test_space_permissions():
    """Test if space permissions are properly configured"""
    print(f"\n🧪 TESTING SPACE PERMISSIONS")
    print("="*60)
    
    base_url = 'https://art.iyc.ishafoundation.org'
    auth = HTTPBasicAuth('script', 'createpage')
    
    try:
        url = f"{base_url}/rest/api/space/TEMP?expand=permissions"
        response = requests.get(url, auth=auth, timeout=30)
        
        print(f"📤 Space API response: {response.status_code}")
        
        if response.status_code == 200:
            space_data = response.json()
            permissions = space_data.get('permissions', [])
            
            print(f"✅ Space permissions accessible")
            print(f"📋 Permission count: {len(permissions)}")
            
            if len(permissions) > 0:
                print(f"📝 Sample permissions:")
                for i, perm in enumerate(permissions[:3]):
                    print(f"   {i+1}. {perm.get('operation', 'Unknown')}")
            
            return True
        else:
            print(f"❌ Could not access space permissions: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing space permissions: {str(e)}")
        return False

def test_standard_confluence_api():
    """Test if standard Confluence API is now working"""
    print(f"\n🧪 TESTING STANDARD CONFLUENCE API")
    print("="*60)
    
    base_url = 'https://art.iyc.ishafoundation.org'
    auth = HTTPBasicAuth('script', 'createpage')
    page_id = "151139217"
    
    try:
        # Test restriction endpoint
        url = f"{base_url}/rest/api/content/{page_id}/restriction"
        response = requests.get(url, auth=auth, timeout=30)
        
        print(f"📤 Restriction API response: {response.status_code}")
        
        if response.status_code == 200:
            print(f"✅ Standard Confluence API: WORKING")
            return True
        elif response.status_code == 405:
            print(f"❌ Standard Confluence API: Still disabled (405)")
            return False
        else:
            print(f"⚠️ Standard Confluence API: Status {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing standard API: {str(e)}")
        return False

def main():
    """Run all ScriptRunner solution tests"""
    print("🧪 SCRIPTRUNNER SOLUTION TESTING SUITE")
    print("🕐 Started at:", datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
    
    # Test 1: Space permissions
    space_result = test_space_permissions()
    
    # Test 2: Standard Confluence API
    api_result = test_standard_confluence_api()
    
    # Test 3: ScriptRunner endpoints
    test_scriptrunner_endpoints()
    
    print("\n" + "="*60)
    print("🎯 SCRIPTRUNNER TESTING COMPLETE")
    print("="*60)
    
    print(f"\n📊 SUMMARY:")
    print(f"   🏢 Space permissions: {'WORKING' if space_result else 'NEEDS SETUP'}")
    print(f"   🔧 Standard API: {'WORKING' if api_result else 'NEEDS SETUP'}")
    print(f"   🚀 ScriptRunner endpoints: CHECK RESULTS ABOVE")
    
    print(f"\n💡 NEXT STEPS:")
    if not space_result:
        print(f"   1. Run the ScriptRunner setup script to enable space permissions")
    if not api_result:
        print(f"   2. Configure standard Confluence API access")
    print(f"   3. Create ScriptRunner REST endpoints as per the guide")
    print(f"   4. Update your Python script to use the new endpoints")

if __name__ == "__main__":
    main()
