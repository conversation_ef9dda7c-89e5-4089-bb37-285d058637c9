# 🔄 JIRA-Confluence Permission Sync

A robust, production-ready script that automatically synchronizes JIRA ticket assignments with Confluence page permissions.

## 🚀 Features

- **Real-time Sync**: Monitors JIRA tickets updated in the last minute
- **Robust Retry Mechanism**: 20 attempts with exponential backoff
- **Username Mapping**: Handles different usernames between JIRA and Confluence
- **Comprehensive Logging**: Detailed logs with emojis for easy monitoring
- **Cache System**: Tracks previous assignees to optimize permission changes
- **Error Handling**: Graceful handling of API failures and network issues
- **Testing Suite**: Built-in comprehensive testing functionality

## 📋 Requirements

- Python 3.6+
- `requests` library
- Network access to JIRA and Confluence instances

## ⚙️ Configuration

### 1. URLs and Credentials
The script is pre-configured for:
- **JIRA**: `https://servicedesk.isha.in`
- **Confluence**: `https://art.iyc.ishafoundation.org`
- **Permission API**: `https://gnanetra.iyc.ishafoundation.org/ArApi/conf/`

### 2. Username Mapping
Update the `USERNAME_MAPPING` dictionary in `app.py` for users with different usernames:

```python
USERNAME_MAPPING = {
    'jira.username': 'confluence.username',
    'yogesh.ramprabhu': 'yogesh ramprabhu',
    'rohit.dutt': 'Rohit Dutt',
    # Add more mappings as needed
}
```

## 🎯 Usage

### Normal Execution
```bash
python app.py
```

### Testing
```bash
# Run comprehensive system tests
python app.py test

# Test specific tickets only
python app.py test-tickets

# Run validation script
python validate_config.py

# Run test runner
python test_runner.py
```

## 🔍 How It Works

### 1. Initialization & Setup ⚙️
- Imports required libraries (requests, datetime, base64)
- Configures JIRA and Confluence URLs
- Sets up authentication and session
- Defines time range for last 1 minute

### 2. Fetches JIRA Tickets 📋
- Builds JQL query for recent updates
- Uses robust retry mechanism (20 attempts with exponential backoff)
- Fetches only required fields: `key`, `summary`, `assignee`, `customfield_111118`

### 3. Extract Page Title & Get page_id 📄
- Extracts page title from `customfield_111118`
- Calls `GET /rest/api/content?title=...`
- Returns `page_id` or skips if not found

### 4. Permission Management Logic 🔐
- Gets current JIRA assignee from issue
- Grants access to current assignee on Confluence page
- Removes access from previous assignees only
- Updates cache with new assignee information

## 📊 Testing Results

The script has been tested with the following JIRA tickets:
- ✅ **AT-59896**: NT-TamTA_O123_Testing-For-Sorting-Project-System (Assigned: Ancy Ravindra)
- ✅ **AT-59894**: NT-TamGC_O123_Testing-For-Sorting-Project-System (Unassigned)
- ✅ **AT-59895**: NT-EngTA_O123_Testing-For-Sorting-Project-System (Assigned: Shriprada Aithal)
- ✅ **AT-59898**: NT-TamMC_O123_Testing-For-Sorting-Project-System (Assigned: Yogesh Ramprabhu)
- ✅ **AT-59899**: NT-Chants_O123_Testing-For-Sorting-Project-System (Assigned: Shriprada Aithal)

All tickets successfully:
- ✅ Connected to JIRA API
- ✅ Retrieved ticket information
- ✅ Found Confluence page IDs
- ✅ Identified current assignees
- ✅ Ready for permission sync

## 📁 Files

- `app.py` - Main sync script
- `validate_config.py` - Configuration validation script
- `test_runner.py` - Comprehensive test runner
- `jira_assignee_cache.json` - Cache file (auto-generated)
- `sync_log.txt` - Execution log (auto-generated)

## 🔧 Troubleshooting

### Common Issues

1. **Authentication Errors**
   - Verify credentials are correct
   - Check network connectivity
   - Run `python validate_config.py`

2. **Page Not Found**
   - Verify Confluence link in JIRA custom field
   - Check page title extraction logic
   - Ensure page exists and is accessible

3. **Permission API Errors**
   - Check if username exists in Confluence
   - Add username mapping if needed
   - Verify API endpoint accessibility

### Debug Mode
Enable verbose logging by modifying the script or running tests:
```bash
python app.py test  # Shows detailed test output
```

## 📈 Monitoring

The script provides comprehensive logging:
- 🚀 Startup and configuration
- 📋 JIRA query and results
- 📄 Page ID resolution
- 🔄 Permission changes
- ✅ Success confirmations
- ❌ Error details

## 🔄 Automation

For continuous monitoring, set up a cron job or scheduled task:
```bash
# Run every minute
* * * * * /usr/bin/python3 /path/to/app.py
```

## 🛡️ Security

- Credentials are base64 encoded (not encrypted)
- Uses HTTPS for all API calls
- Implements proper timeout handling
- Graceful error handling prevents data corruption

## 📞 Support

For issues or questions:
1. Check the logs in `sync_log.txt`
2. Run validation: `python validate_config.py`
3. Run tests: `python app.py test`
4. Review error messages and status codes
