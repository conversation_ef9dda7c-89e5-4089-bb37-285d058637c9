#!/usr/bin/env python3
"""
🔍 FIND CORRECT API
Find the correct API endpoints and parameters for granting page permissions
"""

import requests
from requests.auth import HTTPBasicAuth
import json
from datetime import datetime

def test_api_variations():
    """Test different API endpoint variations and parameter formats"""
    print("🔍 TESTING API VARIATIONS")
    print("="*60)
    
    page_id = "151139217"
    test_user = "rohit.dutt"
    
    # Test different base URLs
    base_urls = [
        "https://art.iyc.ishafoundation.org",
        "https://gnanetra.iyc.ishafoundation.org",
    ]
    
    # Test different API paths
    api_paths = [
        "/ArApi/conf/add-perm",
        "/ArApi/add-perm", 
        "/ArApi/conf/grant-perm",
        "/ArApi/grant-perm",
        "/ArApi/conf/addperm",
        "/ArApi/addperm",
        "/api/conf/add-perm",
        "/api/add-perm",
        "/rest/api/conf/add-perm",
        "/rest/ArApi/conf/add-perm",
    ]
    
    # Test different parameter formats
    param_formats = [
        f"?pid={page_id}&user={test_user}",
        f"?page_id={page_id}&user={test_user}",
        f"?id={page_id}&username={test_user}",
        f"?pageid={page_id}&username={test_user}",
        f"?pid={page_id}&username={test_user}",
        f"?page={page_id}&user={test_user}",
    ]
    
    # Test different authentication methods
    auth_methods = [
        None,  # No auth
        HTTPBasicAuth('script', 'createpage'),  # Confluence auth
    ]
    
    working_endpoints = []
    
    for base_url in base_urls:
        for api_path in api_paths:
            for params in param_formats:
                for auth in auth_methods:
                    
                    full_url = base_url + api_path + params
                    auth_desc = "with auth" if auth else "no auth"
                    
                    try:
                        # Test POST method
                        response = requests.post(full_url, auth=auth, timeout=10)
                        
                        # Check if response is not HTML (indicates API working)
                        is_html = response.text.strip().startswith('<!DOCTYPE') or response.text.strip().startswith('<html')
                        
                        if response.status_code == 200 and not is_html:
                            print(f"✅ WORKING: {full_url} ({auth_desc})")
                            print(f"   Response: {response.text[:100]}")
                            working_endpoints.append((full_url, auth_desc, response.text))
                        elif response.status_code == 200 and is_html:
                            print(f"⚠️ HTML Response: {full_url} ({auth_desc}) - API exists but wrong params")
                        elif response.status_code in [400, 401, 403]:
                            print(f"🔄 Auth/Param issue: {full_url} ({auth_desc}) - Status {response.status_code}")
                        elif response.status_code == 404:
                            pass  # Skip 404s to reduce noise
                        else:
                            print(f"❓ Status {response.status_code}: {full_url} ({auth_desc})")
                            
                    except Exception as e:
                        pass  # Skip connection errors
    
    print(f"\n📊 SUMMARY: Found {len(working_endpoints)} working endpoints")
    for url, auth, response in working_endpoints:
        print(f"   ✅ {url} ({auth}): {response[:50]}")
    
    return working_endpoints

def test_confluence_standard_permissions():
    """Test if we can use standard Confluence API for permissions"""
    print(f"\n🔍 TESTING STANDARD CONFLUENCE PERMISSIONS")
    print("="*60)
    
    confUrl = 'https://art.iyc.ishafoundation.org'
    conf_username = 'script'
    conf_password = 'createpage'
    auth = HTTPBasicAuth(conf_username, conf_password)
    
    page_id = "151139217"
    test_user = "rohit.dutt"
    
    # Method 1: Try to add page restrictions
    try:
        print("1️⃣ Testing page restriction addition...")
        
        # First check if page has restrictions
        url = f"{confUrl}/rest/api/content/{page_id}?expand=restrictions"
        response = requests.get(url, auth=auth, timeout=30)
        
        if response.status_code == 200:
            page_data = response.json()
            restrictions = page_data.get('restrictions', {})
            
            print(f"   📋 Current restrictions: {json.dumps(restrictions, indent=2)}")
            
            # Try to add user restriction
            restriction_url = f"{confUrl}/rest/api/content/{page_id}/restriction"
            
            # Create restriction data
            restriction_data = {
                "operation": "read",
                "restrictions": {
                    "user": [
                        {
                            "type": "known",
                            "username": test_user
                        }
                    ]
                }
            }
            
            headers = {'Content-Type': 'application/json'}
            
            # Try POST
            post_response = requests.post(restriction_url, 
                                        json=restriction_data, 
                                        auth=auth, 
                                        headers=headers,
                                        timeout=30)
            
            print(f"   📤 POST restriction: {post_response.status_code}")
            if post_response.text:
                print(f"      Response: {post_response.text[:200]}")
            
            # Try PUT
            put_response = requests.put(restriction_url, 
                                      json=restriction_data, 
                                      auth=auth, 
                                      headers=headers,
                                      timeout=30)
            
            print(f"   📤 PUT restriction: {put_response.status_code}")
            if put_response.text:
                print(f"      Response: {put_response.text[:200]}")
                
        else:
            print(f"   ❌ Could not get page data: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Error testing standard permissions: {str(e)}")

def test_space_permissions():
    """Test if we need to work at space level instead of page level"""
    print(f"\n🔍 TESTING SPACE-LEVEL PERMISSIONS")
    print("="*60)
    
    confUrl = 'https://art.iyc.ishafoundation.org'
    conf_username = 'script'
    conf_password = 'createpage'
    auth = HTTPBasicAuth(conf_username, conf_password)
    
    page_id = "151139217"
    
    try:
        # Get page to find space
        url = f"{confUrl}/rest/api/content/{page_id}?expand=space"
        response = requests.get(url, auth=auth, timeout=30)
        
        if response.status_code == 200:
            page_data = response.json()
            space_key = page_data.get('space', {}).get('key', '')
            space_name = page_data.get('space', {}).get('name', '')
            
            print(f"   📚 Space: {space_name} ({space_key})")
            
            if space_key:
                # Check space permissions
                space_url = f"{confUrl}/rest/api/space/{space_key}?expand=permissions"
                space_response = requests.get(space_url, auth=auth, timeout=30)
                
                if space_response.status_code == 200:
                    space_data = space_response.json()
                    permissions = space_data.get('permissions', [])
                    
                    print(f"   🔒 Space has {len(permissions)} permission entries")
                    
                    # Show some permissions
                    for i, perm in enumerate(permissions[:3]):
                        print(f"      {i+1}. {perm.get('operation', 'Unknown')}: {perm.get('anonymousAccess', False)}")
                        
                    # Check if space allows individual page restrictions
                    print(f"   💡 Space might control page access - individual page restrictions may not work")
                else:
                    print(f"   ❌ Could not get space permissions: {space_response.status_code}")
        else:
            print(f"   ❌ Could not get page data: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Error testing space permissions: {str(e)}")

def main():
    """Run API discovery"""
    print("🔍 API DISCOVERY SUITE")
    print("🕐 Started at:", datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
    
    # Test 1: API variations
    working_endpoints = test_api_variations()
    
    # Test 2: Standard Confluence API
    test_confluence_standard_permissions()
    
    # Test 3: Space permissions
    test_space_permissions()
    
    print("\n" + "="*60)
    print("🎯 API DISCOVERY COMPLETE")
    print("="*60)
    
    if working_endpoints:
        print(f"\n🎉 FOUND {len(working_endpoints)} WORKING ENDPOINTS!")
        for url, auth, response in working_endpoints:
            print(f"   ✅ {url} ({auth})")
    else:
        print(f"\n❌ NO WORKING GRANT PERMISSION ENDPOINTS FOUND")
        print(f"💡 RECOMMENDATION:")
        print(f"   - The page might inherit permissions from space")
        print(f"   - Individual page restrictions might not be enabled")
        print(f"   - The API might require different authentication")
        print(f"   - Consider using workflow assignment instead")

if __name__ == "__main__":
    main()
