import requests
from requests.auth import HTTPBasicAuth
import json
import datetime
import time
import base64

# ========== CONFIG ==========
# Confluence credentials & session
session = requests.Session()
username = 'script'
password = 'createpage'
confUrl = 'https://art.iyc.ishafoundation.org'
confcwUrl = 'https://art.iyc.ishafoundation.org/rest/cw/1/content'

# Workflow states
Statuses = ['Transcription', 'Transcribed', 'I Proofed', 'II Proofed', 'Proof-Read']
TrStatuses = ['Transcription', 'I Proofing', 'II Proofing', 'Proof-Reading']

# Jira credentials & API
jira_url = "https://servicedesk.isha.in"
EMAIL = 'archives.script'
API_TOKEN = "@utomate"

# Decode Confluence user:pass (currently empty, you must fill base64 string)
decoded_str = base64.b64decode('YXJjaGl2ZXMuc2NyaXB0OkB1dG9tYXRl').decode('utf-8')  # <-- add your real base64 string
Cusername, Cpassword = decoded_str.split(":")

# Auth
auth = HTTPBasicAuth(Cusername, Cpassword)
api_url = f"{jira_url}/rest/api/2/search"

HEADERS = {"Content-Type": "application/json"}
CHECK_VALIDITY_PERIOD = 2  # minutes


# ========== FUNCTIONS ==========

def get_recent_jira_tickets():
    """Fetch Jira tickets updated in last 1 min."""
    now = datetime.datetime.utcnow()
    one_min_ago = now - datetime.timedelta(minutes=1)

    jql = f'project = "Ar Transcription" AND type="Not Transcribed Contents" ' \
          f'AND updated >= "{one_min_ago.strftime("%Y-%m-%d %H:%M")}" ' \
          f'AND updated < "{now.strftime("%Y-%m-%d %H:%M")}"'

    payload = {"jql": jql, "fields": ["summary", "assignee", "status", "customfield_111118"]}
    resp = requests.post(api_url, headers=HEADERS, auth=auth, json=payload)

    if resp.status_code != 200:
        print(f"❌ Jira fetch failed: {resp.status_code} {resp.text}")
        return []

    return resp.json().get("issues", [])


def get_confluence_page_id(page_title):
    """Find Confluence page ID by title."""
    url = f"{confUrl}/rest/api/content"
    params = {"title": page_title}
    resp = session.get(url, auth=(username, password), headers=HEADERS, params=params)

    if resp.status_code != 200:
        print(f"❌ Confluence search failed: {resp.status_code} {resp.text}")
        return None

    results = resp.json().get("results", [])
    if not results:
        print(f"⚠️ No Confluence page found for title {page_title}")
        return None

    return results[0]["id"]


def get_confluence_assignee(page_id):
    """Fetch current Confluence assignee."""
    url = f"{confcwUrl}/{page_id}/status?expand=approvals"
    resp = session.get(url, auth=(username, password), headers=HEADERS)

    if resp.status_code != 200:
        print(f"❌ Confluence status fetch failed: {resp.status_code} {resp.text}")
        return None

    approvals = resp.json().get("approvals", [])
    if approvals and "userName" in approvals[0]:
        return approvals[0]["userName"]
    return None


def update_confluence_assignee(page_id, new_assignee):
    """Update Confluence page assignee to match Jira."""
    url = f"{confcwUrl}/{page_id}/approval"
    payload = {"userName": new_assignee}
    resp = session.post(url, auth=(username, password), headers=HEADERS, json=payload)

    if resp.status_code not in [200, 204]:
        print(f"❌ Failed to update assignee: {resp.status_code} {resp.text}")
    else:
        print(f"✅ Assignee updated → {new_assignee}")


def remove_old_permissions(page_id, keep_user):
    """Remove all old users except current Jira assignee."""
    url = f"{confUrl}/ArApi/conf/rm-perm"
    payload = {"pageId": page_id, "keepUser": keep_user}
    resp = session.post(url, auth=(username, password), headers=HEADERS, json=payload)

    if resp.status_code not in [200, 204]:
        print(f"❌ Permission cleanup failed: {resp.status_code} {resp.text}")
    else:
        print(f"🧹 Old permissions removed, kept {keep_user}")


# ========== MAIN ==========

def main():
    issues = get_recent_jira_tickets()
    print(f"🔎 Found {len(issues)} updated issues")

    for issue in issues:
        fields = issue["fields"]
        jira_assignee = fields["assignee"]["name"] if fields["assignee"] else None
        conf_link = fields.get("customfield_111118")

        if not jira_assignee or not conf_link:
            continue

        page_title = conf_link.strip()
        page_id = get_confluence_page_id(page_title)
        if not page_id:
            continue

        conf_assignee = get_confluence_assignee(page_id)

        if jira_assignee != conf_assignee:
            print(f"⚡ Sync needed: Jira={jira_assignee}, Confluence={conf_assignee}")
            update_confluence_assignee(page_id, jira_assignee)
            remove_old_permissions(page_id, jira_assignee)
        else:
            print(f"✔ Already in sync: {jira_assignee}")

if __name__ == "__main__":
    main()
