#!/usr/bin/env python3
"""
🔍 COMPREHENSIVE DIAGNOSTIC SCRIPT
Tests all components and identifies real issues
"""

import requests
from requests.auth import HTTPBasicAuth
import json
import base64
import urllib.parse
from datetime import datetime

def test_jira_connection():
    """Test JIRA connection and fetch real ticket data"""
    print("\n" + "="*60)
    print("🔍 TESTING JIRA CONNECTION & REAL TICKET DATA")
    print("="*60)
    
    jira_url = "https://servicedesk.isha.in"
    decoded_str = base64.b64decode('YXJjaGl2ZXMuc2NyaXB0OkB1dG9tYXRl').decode('utf-8')
    username, password = decoded_str.split(":")
    auth = HTTPBasicAuth(username, password)
    
    # Test specific tickets
    test_tickets = ["AT-59896", "AT-59894", "AT-59895", "AT-59898", "AT-59899"]
    
    for ticket in test_tickets:
        try:
            api_url = f"{jira_url}/rest/api/2/search"
            params = {
                'jql': f'key = "{ticket}"',
                'fields': 'key,summary,assignee,customfield_111118'
            }
            
            response = requests.get(api_url, params=params, auth=auth, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                if data['issues']:
                    issue = data['issues'][0]
                    assignee = issue['fields']['assignee']
                    confluence_link = issue['fields'].get('customfield_111118')
                    
                    print(f"\n✅ {ticket}: SUCCESS")
                    print(f"   📝 Summary: {issue['fields']['summary']}")
                    print(f"   👤 Assignee: {assignee['name'] if assignee else 'Unassigned'} ({assignee['displayName'] if assignee else 'N/A'})")
                    print(f"   🔗 Confluence Link: {confluence_link}")
                    
                    # Test page ID extraction
                    if confluence_link:
                        title = confluence_link.split('/')[-1].strip()
                        print(f"   📄 Extracted Title: '{title}'")
                        
                        # Test Confluence page lookup
                        test_confluence_page_lookup(ticket, title)
                    
                else:
                    print(f"❌ {ticket}: Not found in JIRA")
            else:
                print(f"❌ {ticket}: JIRA API error {response.status_code}")
                
        except Exception as e:
            print(f"❌ {ticket}: Exception - {str(e)}")

def test_confluence_page_lookup(ticket, title):
    """Test Confluence page lookup"""
    confUrl = 'https://art.iyc.ishafoundation.org'
    username = 'script'
    password = 'createpage'
    
    try:
        encoded_title = urllib.parse.quote(title)
        url = f'{confUrl}/rest/api/content/?title={encoded_title}'
        
        response = requests.get(url, auth=HTTPBasicAuth(username, password), timeout=30)
        
        if response.status_code == 200:
            page_data = response.json()
            if 'results' in page_data and len(page_data['results']) > 0:
                page_id = page_data['results'][0]['id']
                print(f"   ✅ Page ID: {page_id}")
                
                # Test permission API endpoints
                test_permission_apis(page_id, "test.user")
            else:
                print(f"   ❌ Page not found in Confluence")
        else:
            print(f"   ❌ Confluence API error {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Confluence lookup error: {str(e)}")

def test_permission_apis(page_id, test_username):
    """Test permission API endpoints with different approaches"""
    print(f"   🔧 Testing Permission APIs for page {page_id}...")
    
    # Test different API endpoints and formats
    api_bases = [
        "https://gnanetra.iyc.ishafoundation.org/ArApi/conf",
        "https://gnanetra.iyc.ishafoundation.org/ArApi",
        "https://art.iyc.ishafoundation.org/ArApi/conf",
    ]
    
    endpoints = [
        "add-perm",
        "addperm", 
        "add_perm",
        "grant-access",
        "grant_access"
    ]
    
    for api_base in api_bases:
        for endpoint in endpoints:
            test_url = f"{api_base}/{endpoint}?pid={page_id}&user={test_username}"
            
            try:
                # Test with HEAD request first (safer)
                response = requests.head(test_url, timeout=10)
                status = response.status_code
                
                if status == 200:
                    print(f"   ✅ WORKING: {test_url}")
                elif status == 405:  # Method not allowed - endpoint exists but wrong method
                    print(f"   🔄 EXISTS (wrong method): {test_url}")
                elif status == 404:
                    print(f"   ❌ NOT FOUND: {test_url}")
                else:
                    print(f"   ⚠️ STATUS {status}: {test_url}")
                    
            except Exception as e:
                print(f"   ❌ ERROR: {test_url} - {str(e)}")

def test_alternative_permission_methods():
    """Test alternative methods for managing Confluence permissions"""
    print("\n" + "="*60)
    print("🔍 TESTING ALTERNATIVE PERMISSION METHODS")
    print("="*60)
    
    confUrl = 'https://art.iyc.ishafoundation.org'
    username = 'script'
    password = 'createpage'
    auth = HTTPBasicAuth(username, password)
    
    # Test if we can use standard Confluence REST API for permissions
    test_page_id = "151139217"  # From our previous tests
    
    try:
        # Get page restrictions
        url = f"{confUrl}/rest/api/content/{test_page_id}/restriction"
        response = requests.get(url, auth=auth, timeout=30)
        
        if response.status_code == 200:
            restrictions = response.json()
            print("✅ Can access page restrictions via standard API")
            print(f"   Current restrictions: {json.dumps(restrictions, indent=2)}")
            
            # Test if we can modify restrictions
            test_standard_api_permissions(test_page_id, auth)
        else:
            print(f"❌ Cannot access page restrictions: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error testing standard API: {str(e)}")

def test_standard_api_permissions(page_id, auth):
    """Test standard Confluence API for permission management"""
    confUrl = 'https://art.iyc.ishafoundation.org'
    
    # Test adding user restriction
    url = f"{confUrl}/rest/api/content/{page_id}/restriction"
    
    test_data = {
        "operation": "read",
        "restrictions": {
            "user": [
                {
                    "type": "known",
                    "username": "test.user"
                }
            ]
        }
    }
    
    try:
        response = requests.post(url, 
                               json=test_data, 
                               auth=auth, 
                               headers={'Content-Type': 'application/json'},
                               timeout=30)
        
        if response.status_code in [200, 201]:
            print("✅ Standard API permissions work!")
            print(f"   Response: {response.json()}")
        else:
            print(f"⚠️ Standard API returned {response.status_code}")
            print(f"   Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Standard API error: {str(e)}")

def test_user_lookup():
    """Test user lookup in both systems"""
    print("\n" + "="*60)
    print("🔍 TESTING USER LOOKUP")
    print("="*60)
    
    # Test JIRA users
    jira_url = "https://servicedesk.isha.in"
    decoded_str = base64.b64decode('YXJjaGl2ZXMuc2NyaXB0OkB1dG9tYXRl').decode('utf-8')
    jira_username, jira_password = decoded_str.split(":")
    jira_auth = HTTPBasicAuth(jira_username, jira_password)
    
    # Test Confluence users
    confUrl = 'https://art.iyc.ishafoundation.org'
    conf_username = 'script'
    conf_password = 'createpage'
    conf_auth = HTTPBasicAuth(conf_username, conf_password)
    
    test_users = ["ancy.ravindra", "yogesh.ramprabhu", "shriprada.aithal"]
    
    for user in test_users:
        print(f"\n👤 Testing user: {user}")
        
        # Test in JIRA
        try:
            url = f"{jira_url}/rest/api/2/user?username={user}"
            response = requests.get(url, auth=jira_auth, timeout=10)
            if response.status_code == 200:
                user_data = response.json()
                print(f"   ✅ JIRA: {user_data.get('displayName', 'Unknown')}")
            else:
                print(f"   ❌ JIRA: Not found ({response.status_code})")
        except Exception as e:
            print(f"   ❌ JIRA: Error - {str(e)}")
        
        # Test in Confluence
        try:
            url = f"{confUrl}/rest/api/user?username={user}"
            response = requests.get(url, auth=conf_auth, timeout=10)
            if response.status_code == 200:
                user_data = response.json()
                print(f"   ✅ Confluence: {user_data.get('displayName', 'Unknown')}")
            else:
                print(f"   ❌ Confluence: Not found ({response.status_code})")
        except Exception as e:
            print(f"   ❌ Confluence: Error - {str(e)}")

def main():
    """Run comprehensive diagnostics"""
    print("🔍 COMPREHENSIVE DIAGNOSTIC SCRIPT")
    print("🕐 Started at:", datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
    
    # Run all tests
    test_jira_connection()
    test_alternative_permission_methods()
    test_user_lookup()
    
    print("\n" + "="*60)
    print("🎯 DIAGNOSTIC COMPLETE")
    print("="*60)
    print("🕐 Completed at:", datetime.now().strftime('%Y-%m-%d %H:%M:%S'))

if __name__ == "__main__":
    main()
