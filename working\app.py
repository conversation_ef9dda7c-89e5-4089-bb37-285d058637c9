import requests
from requests.auth import HTTPBasicAuth
import json
import time
import datetime
import base64
import urllib.parse

# Initialize global variables
totalupdates = 0

# Time range for checking recently updated tickets
time_now = datetime.datetime.now().strftime("%Y-%m-%d %H:%M")
time_one_minute_ago = (datetime.datetime.now() - datetime.timedelta(minutes=1)).strftime("%Y-%m-%d %H:%M")

# Confluence configuration
username = 'script'
password = 'createpage'
confUrl = 'https://art.iyc.ishafoundation.org'
confcwUrl = 'https://art.iyc.ishafoundation.org/rest/cw/1/content'

# JIRA configuration
jira_url = "https://servicedesk.isha.in"
decoded_str = base64.b64decode('YXJjaGl2ZXMuc2NyaXB0OkB1dG9tYXRl').decode('utf-8')
Cusername, Cpassword = decoded_str.split(":")
auth = HTTPBasicAuth(Cusername, Cpassword)
api_url = f"{jira_url}/rest/api/2/search"

session = requests.Session()

def fetch_jira_issues():
    """Fetch JIRA issues updated in the last minute"""
    query = f'project = "Ar Transcription" and type="Not Transcribed Contents" and updated >= "{time_one_minute_ago}" and updated < "{time_now}"'
    print(f"JIRA Query: {query}")
    
    all_issues = []
    max_results = 500
    start_at = 0
    
    while True:
        params = {
            'jql': query,
            'startAt': start_at,
            'maxResults': max_results,
            'fields': 'key,summary,assignee,customfield_111118'
        }
        
        # Retry logic for JIRA API
        max_tries = 20
        tries = 0
        while tries < max_tries:
            response = session.get(api_url, params=params, auth=auth)
            time.sleep(0.2)
            if response.status_code == 200:
                break
            tries += 1
            print(f'JIRA API retry {tries}/{max_tries}')
            time.sleep(5)
        
        if response.status_code != 200:
            print(f"Failed to fetch JIRA issues: {response.status_code}")
            break
            
        data = response.json()
        all_issues.extend(data['issues'])
        
        if start_at + max_results >= data['total']:
            break
        start_at += max_results
    
    return all_issues

def get_confluence_page_id(jira_issue, title):
    """Get Confluence page ID from title"""
    if not title or title.strip() == '':
        print(f'Empty title for JIRA ticket: {jira_issue["key"]}')
        return None
    
    # URL encode the title
    encoded_title = urllib.parse.quote(title)
    url = f'{confUrl}/rest/api/content/?title={encoded_title}'
    
    try:
        response = requests.get(url, auth=HTTPBasicAuth(username, password), timeout=30)
        
        if response.status_code != 200:
            print(f'Could not get Confluence page for {jira_issue["key"]}: API error {response.status_code}')
            return None
        
        page_data = response.json()
        
        if 'results' not in page_data or len(page_data['results']) == 0:
            print(f'No Confluence page found with title "{title}" for {jira_issue["key"]}')
            return None
        
        return page_data['results'][0]['id']
        
    except Exception as e:
        print(f'Error getting Confluence page ID for {jira_issue["key"]}: {str(e)}')
        return None

def get_confluence_assignee(page_id):
    """Get current assignee from Confluence page"""
    url = f'{confcwUrl}/{page_id}/status?expand=approvals'
    
    try:
        response = requests.get(url, auth=HTTPBasicAuth(username, password))
        
        if response.status_code != 200:
            print(f'Could not get Confluence status for page {page_id}')
            return None
        
        page_data = response.json()
        
        # Extract current approver/assignee
        if 'approvals' in page_data and len(page_data['approvals']) > 0:
            approvers = page_data['approvals'][0].get('approvers', [])
            if approvers and len(approvers) > 0 and 'user' in approvers[0] and approvers[0]['user']:
                return approvers[0]['user']['name']
        
        return ''  # No assignee
        
    except Exception as e:
        print(f'Error getting Confluence assignee for page {page_id}: {str(e)}')
        return None

def update_confluence_assignee(page_id, jira_assignee):
    """Update assignee on Confluence page"""
    global totalupdates
    
    # Get current workflow state to determine which stage to assign
    url = f'{confcwUrl}/{page_id}/status?expand=approvals'
    
    try:
        response = requests.get(url, auth=HTTPBasicAuth(username, password))
        if response.status_code != 200:
            print(f'Could not get current workflow state for page {page_id}')
            return False
        
        page_data = response.json()
        current_state = page_data['state']['name']
        
        # Map Confluence states to workflow stages
        stage_mapping = {
            'Transcription': 'Transcription',
            'Transcribed': 'I Proofing', 
            'I Proofed': 'II Proofing',
            'II Proofed': 'Proof-Reading'
        }
        
        if current_state not in stage_mapping:
            print(f'Unknown workflow state "{current_state}" for page {page_id}')
            return False
        
        target_stage = stage_mapping[current_state]
        
        # Update assignee
        update_url = f'{confcwUrl}/{page_id}/approvals/assign?expand=state,states,actions,approvals&admin=true'
        
        assignee_data = {
            'name': target_stage,
            'assignees': [{'username': jira_assignee}] if jira_assignee else []
        }
        
        update_response = requests.patch(
            update_url,
            data=json.dumps(assignee_data),
            auth=HTTPBasicAuth(username, password),
            headers={'Content-Type': 'application/json'}
        )
        
        if update_response.status_code == 200:
            totalupdates += 1
            print(f'✅ Successfully updated assignee to "{jira_assignee}" for page {page_id}')
            return True
        else:
            print(f'❌ Failed to update assignee for page {page_id}: {update_response.status_code}')
            return False
            
    except Exception as e:
        print(f'Error updating Confluence assignee for page {page_id}: {str(e)}')
        return False

def remove_old_user_permission(page_id, old_user):
    """Remove user permission from Confluence page"""
    if not old_user:
        return
    
    url = f'https://gnanetra.iyc.ishafoundation.org/ArApi/conf/rm-perm?pid={page_id}&user={old_user}'
    
    try:
        response = requests.post(url)
        if response.status_code == 200:
            print(f'🗑️ Removed permission for user "{old_user}" from page {page_id}')
        else:
            print(f'⚠️ Could not remove permission for user "{old_user}" from page {page_id}')
    except Exception as e:
        print(f'Error removing user permission: {str(e)}')

def sync_assignee(jira_issue):
    """Main function to sync assignee between JIRA and Confluence"""
    
    # Extract data from JIRA issue
    jira_key = jira_issue['key']
    jira_assignee = jira_issue['fields']['assignee']['name'] if jira_issue['fields']['assignee'] else ''
    
    # Get Confluence page title from JIRA custom field
    custom_field_value = jira_issue['fields'].get('customfield_111118')
    if not custom_field_value:
        print(f'⚠️ {jira_key}: No Confluence page link found')
        return
    
    # Extract title from the custom field (assuming it's a URL or path)
    if isinstance(custom_field_value, str):
        title = custom_field_value.split('/')[-1].strip()
    else:
        print(f'⚠️ {jira_key}: Invalid custom field format')
        return
    
    # Get Confluence page ID
    page_id = get_confluence_page_id(jira_issue, title)
    if not page_id:
        return
    
    # Get current Confluence assignee
    confluence_assignee = get_confluence_assignee(page_id)
    if confluence_assignee is None:
        return
    
    print(f'📋 {jira_key} -> Page {page_id}')
    print(f'   JIRA assignee: "{jira_assignee}"')
    print(f'   Confluence assignee: "{confluence_assignee}"')
    
    # Compare assignees
    if jira_assignee == confluence_assignee:
        print(f'✓ {jira_key}: Assignees already match - no update needed')
        return
    
    # Update Confluence assignee
    print(f'🔄 {jira_key}: Updating Confluence assignee from "{confluence_assignee}" to "{jira_assignee}"')
    
    if update_confluence_assignee(page_id, jira_assignee):
        # Remove old user permission if there was a previous assignee
        if confluence_assignee and confluence_assignee != jira_assignee:
            remove_old_user_permission(page_id, confluence_assignee)
    
    print()  # Add spacing between issues

def main():
    """Main execution function"""
    global totalupdates
    totalupdates = 0
    
    print("=" * 50)
    print("🚀 JIRA-Confluence Assignee Sync Started")
    print("=" * 50)
    print(f"⏰ Checking updates from {time_one_minute_ago} to {time_now}")
    print()
    
    try:
        # Fetch JIRA issues
        jira_issues = fetch_jira_issues()
        print(f"📊 Found {len(jira_issues)} JIRA issues to process")
        print()
        
        if len(jira_issues) == 0:
            print("✅ No issues to process")
            return
        
        # Process each issue
        for issue in jira_issues:
            try:
                sync_assignee(issue)
            except Exception as e:
                print(f"❌ Error processing {issue['key']}: {str(e)}")
                print()
    
    except Exception as e:
        print(f"❌ Error in main execution: {str(e)}")
    
    print("=" * 50)
    print(f"✅ Sync Complete - Total Updates: {totalupdates}")
    print("=" * 50)

# Log execution
def log_execution():
    """Log script execution"""
    run_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    try:
        with open('assignee-sync-log.txt', 'a', encoding='utf-8') as f:
            f.write(f'Assignee sync ran on: {run_time} - Updates: {totalupdates}\n')
    except Exception as e:
        print(f"Could not write to log file: {str(e)}")

if __name__ == "__main__":
    main()
    log_execution()
